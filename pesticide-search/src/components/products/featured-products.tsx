'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProductWithRelations } from '@/types/search'

export function FeaturedProducts() {
  const [products, setProducts] = useState<ProductWithRelations[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 这里应该从API获取特色产品
    // 为了演示，我们使用模拟数据
    const mockProducts: Partial<ProductWithRelations>[] = [
      {
        id: '1',
        name: '草甘膦异丙胺盐水剂',
        brand: { id: '1', name: '先正达', company: '先正达（中国）投资有限公司' },
        formulation: '水剂',
        concentration: '41%',
        price: 25.50,
        priceUnit: '元/瓶',
        description: '广谱性除草剂，适用于多种作物',
        activeIngredients: [
          {
            activeIngredient: { id: '1', name: '草甘膦异丙胺盐' },
            concentration: '41%'
          }
        ],
        crops: [
          { crop: { id: '1', name: '水稻' } },
          { crop: { id: '2', name: '小麦' } }
        ],
        pests: [
          { pest: { id: '1', name: '杂草' } }
        ]
      },
      {
        id: '2',
        name: '吡虫啉可湿性粉剂',
        brand: { id: '2', name: '拜耳', company: '拜耳作物科学（中国）有限公司' },
        formulation: '可湿性粉剂',
        concentration: '70%',
        price: 18.80,
        priceUnit: '元/袋',
        description: '高效杀虫剂，对蚜虫等害虫有特效',
        activeIngredients: [
          {
            activeIngredient: { id: '2', name: '吡虫啉' },
            concentration: '70%'
          }
        ],
        crops: [
          { crop: { id: '1', name: '水稻' } },
          { crop: { id: '3', name: '棉花' } }
        ],
        pests: [
          { pest: { id: '2', name: '蚜虫' } },
          { pest: { id: '3', name: '飞虱' } }
        ]
      },
      {
        id: '3',
        name: '多菌灵可湿性粉剂',
        brand: { id: '3', name: '江苏扬农', company: '江苏扬农化工股份有限公司' },
        formulation: '可湿性粉剂',
        concentration: '50%',
        price: 12.30,
        priceUnit: '元/袋',
        description: '广谱性杀菌剂，防治多种真菌病害',
        activeIngredients: [
          {
            activeIngredient: { id: '3', name: '多菌灵' },
            concentration: '50%'
          }
        ],
        crops: [
          { crop: { id: '1', name: '水稻' } },
          { crop: { id: '2', name: '小麦' } },
          { crop: { id: '4', name: '玉米' } }
        ],
        pests: [
          { pest: { id: '4', name: '稻瘟病' } },
          { pest: { id: '5', name: '纹枯病' } }
        ]
      }
    ]

    // 模拟API调用延迟
    setTimeout(() => {
      setProducts(mockProducts as ProductWithRelations[])
      setIsLoading(false)
    }, 1000)
  }, [])

  if (isLoading) {
    return (
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            特色产品推荐
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          特色产品推荐
        </h2>
        
        <div className="grid md:grid-cols-3 gap-8">
          {products.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                    {product.name}
                  </h3>
                  {product.price && (
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        ¥{product.price}
                      </div>
                      <div className="text-sm text-gray-500">
                        {product.priceUnit}
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="font-medium">品牌:</span>
                    <span className="ml-1">{product.brand?.name}</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="font-medium">剂型:</span>
                    <span className="ml-1">{product.formulation}</span>
                  </div>
                  
                  {product.concentration && (
                    <div className="flex items-center text-sm text-gray-600">
                      <span className="font-medium">含量:</span>
                      <span className="ml-1">{product.concentration}</span>
                    </div>
                  )}
                </div>

                {product.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {product.description}
                  </p>
                )}

                {/* 有效成分 */}
                {product.activeIngredients && product.activeIngredients.length > 0 && (
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-1">有效成分:</div>
                    <div className="flex flex-wrap gap-1">
                      {product.activeIngredients.slice(0, 2).map((ai, index) => (
                        <span
                          key={index}
                          className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                        >
                          {ai.activeIngredient.name}
                          {ai.concentration && ` (${ai.concentration})`}
                        </span>
                      ))}
                      {product.activeIngredients.length > 2 && (
                        <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                          +{product.activeIngredients.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* 适用作物 */}
                {product.crops && product.crops.length > 0 && (
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-1">适用作物:</div>
                    <div className="flex flex-wrap gap-1">
                      {product.crops.slice(0, 3).map((crop, index) => (
                        <span
                          key={index}
                          className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded"
                        >
                          {crop.crop.name}
                        </span>
                      ))}
                      {product.crops.length > 3 && (
                        <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                          +{product.crops.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* 防治对象 */}
                {product.pests && product.pests.length > 0 && (
                  <div className="mb-6">
                    <div className="text-sm font-medium text-gray-700 mb-1">防治对象:</div>
                    <div className="flex flex-wrap gap-1">
                      {product.pests.slice(0, 3).map((pest, index) => (
                        <span
                          key={index}
                          className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded"
                        >
                          {pest.pest.name}
                        </span>
                      ))}
                      {product.pests.length > 3 && (
                        <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                          +{product.pests.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <Link
                  href={`/products/${product.id}`}
                  className="block w-full text-center bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition-colors"
                >
                  查看详情
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/products"
            className="inline-block bg-gray-600 text-white px-8 py-3 rounded-md hover:bg-gray-700 transition-colors"
          >
            查看更多产品
          </Link>
        </div>
      </div>
    </section>
  )
}
