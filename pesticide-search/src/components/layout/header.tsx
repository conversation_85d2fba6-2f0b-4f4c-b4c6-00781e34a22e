'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSession, signOut } from 'next-auth/react'
// import { UserRole } from '@/types/auth'

export function Header() {
  const { data: session, status } = useSession()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return '管理员'
      case UserRole.AGRONOMIST:
        return '农艺师'
      case UserRole.PESTICIDE_STORE:
        return '农药店'
      case UserRole.BRAND:
        return '品牌方'
      default:
        return '用户'
    }
  }

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <span className="text-xl font-bold text-gray-900">农药搜索</span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/search" className="text-gray-600 hover:text-gray-900">
              产品搜索
            </Link>
            <Link href="/products" className="text-gray-600 hover:text-gray-900">
              产品库
            </Link>
            {session?.user.role === 'AGRONOMIST' && (
              <Link href="/recommendations" className="text-gray-600 hover:text-gray-900">
                我的推荐
              </Link>
            )}
            {(session?.user.role === 'BRAND' || session?.user.role === 'PESTICIDE_STORE') && (
              <Link href="/my-products" className="text-gray-600 hover:text-gray-900">
                我的产品
              </Link>
            )}
            {session?.user.role === 'ADMIN' && (
              <Link href="/admin" className="text-gray-600 hover:text-gray-900">
                管理后台
              </Link>
            )}
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {status === 'loading' ? (
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
            ) : session ? (
              <div className="relative">
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                >
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-green-600">
                      {session.user.name?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="hidden md:block">{session.user.name}</span>
                  <span className="hidden md:block text-sm text-gray-500">
                    ({getRoleDisplayName(session.user.role)})
                  </span>
                </button>

                {isMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      个人资料
                    </Link>
                    <Link
                      href="/settings"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      设置
                    </Link>
                    <button
                      onClick={() => {
                        setIsMenuOpen(false)
                        signOut()
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      退出登录
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  href="/auth/signin"
                  className="text-gray-600 hover:text-gray-900"
                >
                  登录
                </Link>
                <Link
                  href="/auth/signup"
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                >
                  注册
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <div className="flex flex-col space-y-2">
              <Link href="/search" className="text-gray-600 hover:text-gray-900 py-2">
                产品搜索
              </Link>
              <Link href="/products" className="text-gray-600 hover:text-gray-900 py-2">
                产品库
              </Link>
              {session?.user.role === UserRole.AGRONOMIST && (
                <Link href="/recommendations" className="text-gray-600 hover:text-gray-900 py-2">
                  我的推荐
                </Link>
              )}
              {(session?.user.role === UserRole.BRAND || session?.user.role === UserRole.PESTICIDE_STORE) && (
                <Link href="/my-products" className="text-gray-600 hover:text-gray-900 py-2">
                  我的产品
                </Link>
              )}
              {session?.user.role === UserRole.ADMIN && (
                <Link href="/admin" className="text-gray-600 hover:text-gray-900 py-2">
                  管理后台
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
