'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { SearchType, SearchMode, SearchSuggestion } from '@/types/search'

export function SearchSection() {
  const router = useRouter()
  const [query, setQuery] = useState('')
  const [searchType, setSearchType] = useState<SearchType>(SearchType.ALL)
  const [searchMode, setSearchMode] = useState<SearchMode>(SearchMode.FUZZY)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 获取搜索建议
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (query.length < 2) {
        setSuggestions([])
        return
      }

      try {
        const response = await fetch(`/api/search/suggestions?query=${encodeURIComponent(query)}&limit=8`)
        if (response.ok) {
          const data = await response.json()
          setSuggestions(data)
        }
      } catch (error) {
        console.error('Failed to fetch suggestions:', error)
      }
    }

    const debounceTimer = setTimeout(fetchSuggestions, 300)
    return () => clearTimeout(debounceTimer)
  }, [query])

  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return

    setIsLoading(true)
    setShowSuggestions(false)

    const params = new URLSearchParams({
      query: finalQuery,
      type: searchType,
      mode: searchMode
    })

    router.push(`/search?${params.toString()}`)
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.value)
    setSearchType(suggestion.type)
    setShowSuggestions(false)
    handleSearch(suggestion.value)
  }

  const getSearchTypeLabel = (type: SearchType) => {
    switch (type) {
      case SearchType.ALL:
        return '全部'
      case SearchType.PRODUCT_NAME:
        return '产品名称'
      case SearchType.ACTIVE_INGREDIENT:
        return '有效成分'
      case SearchType.CROP:
        return '作物'
      case SearchType.PEST:
        return '病虫草害'
      case SearchType.BRAND:
        return '品牌'
      default:
        return '全部'
    }
  }

  const getSearchModeLabel = (mode: SearchMode) => {
    switch (mode) {
      case SearchMode.FUZZY:
        return '模糊匹配'
      case SearchMode.EXACT:
        return '精确匹配'
      case SearchMode.PHRASE:
        return '短语匹配'
      case SearchMode.SYNONYM:
        return '同义词匹配'
      default:
        return '模糊匹配'
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Search Options */}
      <div className="flex flex-wrap gap-4 mb-6 justify-center">
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">搜索类型:</label>
          <select
            value={searchType}
            onChange={(e) => setSearchType(e.target.value as SearchType)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            {Object.values(SearchType).map((type) => (
              <option key={type} value={type}>
                {getSearchTypeLabel(type)}
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">匹配模式:</label>
          <select
            value={searchMode}
            onChange={(e) => setSearchMode(e.target.value as SearchMode)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            {Object.values(SearchMode).map((mode) => (
              <option key={mode} value={mode}>
                {getSearchModeLabel(mode)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Search Input */}
      <div className="relative">
        <div className="flex">
          <div className="relative flex-1">
            <input
              type="text"
              value={query}
              onChange={(e) => {
                setQuery(e.target.value)
                setShowSuggestions(true)
              }}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch()
                }
              }}
              placeholder="搜索农药产品、有效成分、作物、病虫草害..."
              className="w-full px-6 py-4 text-lg border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            
            {/* Search Icon */}
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          <button
            onClick={() => handleSearch()}
            disabled={isLoading || !query.trim()}
            className="px-8 py-4 bg-green-600 text-white font-medium rounded-r-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '搜索中...' : '搜索'}
          </button>
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {getSearchTypeLabel(suggestion.type)}
                  </span>
                  <span className="text-gray-900">{suggestion.label}</span>
                </div>
                {suggestion.count && (
                  <span className="text-sm text-gray-500">{suggestion.count} 个结果</span>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Quick Search Tags */}
      <div className="mt-6">
        <p className="text-sm text-gray-600 mb-3">热门搜索:</p>
        <div className="flex flex-wrap gap-2">
          {[
            { label: '除草剂', type: SearchType.ACTIVE_INGREDIENT },
            { label: '杀虫剂', type: SearchType.ACTIVE_INGREDIENT },
            { label: '杀菌剂', type: SearchType.ACTIVE_INGREDIENT },
            { label: '水稻', type: SearchType.CROP },
            { label: '小麦', type: SearchType.CROP },
            { label: '玉米', type: SearchType.CROP },
            { label: '蚜虫', type: SearchType.PEST },
            { label: '稻飞虱', type: SearchType.PEST }
          ].map((tag, index) => (
            <button
              key={index}
              onClick={() => {
                setQuery(tag.label)
                setSearchType(tag.type)
                handleSearch(tag.label)
              }}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
            >
              {tag.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
