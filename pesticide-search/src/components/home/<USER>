'use client'

import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { UserRole } from '@/types/auth'

export function RoleBasedContent() {
  const { data: session } = useSession()

  if (!session) {
    return (
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            选择您的身份，获得专属服务
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">农艺师</h3>
              <p className="text-gray-600 mb-6">
                提供专业的产品推荐和技术指导，帮助农户选择最适合的农药产品
              </p>
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• 发布产品推荐</li>
                <li>• 技术指导服务</li>
                <li>• 专业评价产品</li>
              </ul>
              <Link
                href="/auth/signup?role=agronomist"
                className="inline-block bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700"
              >
                注册为农艺师
              </Link>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">农药店</h3>
              <p className="text-gray-600 mb-6">
                展示店铺产品，管理库存信息，为客户提供便捷的购买渠道
              </p>
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• 展示销售产品</li>
                <li>• 管理库存信息</li>
                <li>• 客户服务支持</li>
              </ul>
              <Link
                href="/auth/signup?role=store"
                className="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
              >
                注册为农药店
              </Link>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">品牌方</h3>
              <p className="text-gray-600 mb-6">
                发布产品信息，展示品牌实力，扩大产品影响力和市场覆盖
              </p>
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• 发布产品信息</li>
                <li>• 品牌展示推广</li>
                <li>• 市场数据分析</li>
              </ul>
              <Link
                href="/auth/signup?role=brand"
                className="inline-block bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700"
              >
                注册为品牌方
              </Link>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // 已登录用户的个性化内容
  return (
    <section className="py-16 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          欢迎回来，{session.user.name}
        </h2>
        
        {session.user.role === 'AGRONOMIST' && (
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">我的推荐</h3>
              <p className="text-gray-600 mb-4">管理您发布的产品推荐和技术指导</p>
              <Link
                href="/recommendations"
                className="inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                查看推荐
              </Link>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">发布推荐</h3>
              <p className="text-gray-600 mb-4">为农户推荐合适的农药产品</p>
              <Link
                href="/recommendations/new"
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                发布推荐
              </Link>
            </div>
          </div>
        )}

        {(session.user.role === 'PESTICIDE_STORE' || session.user.role === 'BRAND') && (
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">我的产品</h3>
              <p className="text-gray-600 mb-4">管理您提交的产品信息</p>
              <Link
                href="/my-products"
                className="inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                查看产品
              </Link>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">添加产品</h3>
              <p className="text-gray-600 mb-4">提交新的产品信息</p>
              <Link
                href="/products/new"
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                添加产品
              </Link>
            </div>
          </div>
        )}

        {session.user.role === 'ADMIN' && (
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">用户管理</h3>
              <p className="text-gray-600 mb-4">审核和管理用户账户</p>
              <Link
                href="/admin/users"
                className="inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                管理用户
              </Link>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">产品审核</h3>
              <p className="text-gray-600 mb-4">审核待发布的产品信息</p>
              <Link
                href="/admin/products"
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                审核产品
              </Link>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold mb-4">数据统计</h3>
              <p className="text-gray-600 mb-4">查看平台使用统计</p>
              <Link
                href="/admin/analytics"
                className="inline-block bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
              >
                查看统计
              </Link>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
