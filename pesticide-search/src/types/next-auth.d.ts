import { UserRole, UserStatus } from '@prisma/client'
import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      status: UserStatus
      companyName?: string
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    status: UserStatus
    companyName?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    status: UserStatus
    companyName?: string
  }
}
