// 用户角色枚举
export enum UserRole {
  ADMIN = 'ADMIN',
  AGRONOMIST = 'AGRONOMIST',
  PESTICIDE_STORE = 'PESTICIDE_STORE',
  BRAND = 'BRAND'
}

// 扩展NextAuth的类型定义
declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      role: UserRole
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    role: UserRole
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    role: UserRole
  }
}
