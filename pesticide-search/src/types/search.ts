import { Product, ActiveIngredient, Crop, Pest, Brand, User } from '@prisma/client'

// 搜索类型枚举
export enum SearchType {
  ALL = 'all',
  PRODUCT_NAME = 'product_name',
  ACTIVE_INGREDIENT = 'active_ingredient',
  CROP = 'crop',
  PEST = 'pest',
  BRAND = 'brand'
}

// 搜索模式枚举
export enum SearchMode {
  FUZZY = 'fuzzy',        // 模糊匹配
  EXACT = 'exact',        // 精确匹配
  PHRASE = 'phrase',      // 短语匹配
  SYNONYM = 'synonym'     // 同义词匹配
}

// 搜索筛选条件
export interface SearchFilters {
  // 价格范围
  priceMin?: number
  priceMax?: number
  
  // 有效成分
  activeIngredients?: string[]
  
  // 作物
  crops?: string[]
  
  // 病虫草害
  pests?: string[]
  
  // 品牌
  brands?: string[]
  
  // 剂型
  formulations?: string[]
  
  // 用户角色（针对推荐）
  userRoles?: string[]
  
  // 产品状态
  status?: string[]
}

// 排序选项
export interface SortOptions {
  field: 'name' | 'price' | 'createdAt' | 'updatedAt' | 'relevance'
  order: 'asc' | 'desc'
}

// 搜索请求参数
export interface SearchParams {
  query: string
  type: SearchType
  mode: SearchMode
  filters: SearchFilters
  sort: SortOptions
  page: number
  limit: number
}

// 扩展的产品类型（包含关联数据）
export interface ProductWithRelations extends Product {
  brand: Brand
  submitter: User
  activeIngredients: Array<{
    activeIngredient: ActiveIngredient
    concentration?: string
  }>
  crops: Array<{
    crop: Crop
  }>
  pests: Array<{
    pest: Pest
  }>
  recommendations?: Array<{
    id: string
    title: string
    rating?: number
    user: {
      name: string
      role: string
    }
  }>
}

// 搜索结果
export interface SearchResult {
  products: ProductWithRelations[]
  total: number
  page: number
  limit: number
  totalPages: number
  filters: {
    availableActiveIngredients: Array<{ id: string; name: string; count: number }>
    availableCrops: Array<{ id: string; name: string; count: number }>
    availablePests: Array<{ id: string; name: string; count: number }>
    availableBrands: Array<{ id: string; name: string; count: number }>
    availableFormulations: Array<{ value: string; count: number }>
    priceRange: { min: number; max: number }
  }
}

// 搜索建议
export interface SearchSuggestion {
  type: SearchType
  value: string
  label: string
  count?: number
}

// 搜索历史
export interface SearchHistory {
  id: string
  query: string
  type: SearchType
  timestamp: Date
  resultCount: number
}
