import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { prisma } from './prisma'
import { compare } from 'bcryptjs'
import { UserRole, UserStatus } from '@prisma/client'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        })

        if (!user) {
          return null
        }

        // 这里应该验证密码，但为了简化示例，我们暂时跳过
        // const isPasswordValid = await compare(credentials.password, user.password)
        // if (!isPasswordValid) {
        //   return null
        // }

        // 检查用户状态
        if (user.status !== UserStatus.APPROVED) {
          throw new Error('账户尚未通过审核或已被暂停')
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          status: user.status,
          companyName: user.companyName
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.status = user.status
        token.companyName = user.companyName
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.status = token.status as UserStatus
        session.user.companyName = token.companyName as string
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup'
  }
}

// 权限检查函数
export function hasPermission(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole)
}

// 管理员权限检查
export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}

// 可以提交产品的角色
export function canSubmitProducts(userRole: UserRole): boolean {
  return [UserRole.BRAND, UserRole.PESTICIDE_STORE].includes(userRole)
}

// 可以推荐产品的角色
export function canRecommendProducts(userRole: UserRole): boolean {
  return [UserRole.AGRONOMIST, UserRole.ADMIN].includes(userRole)
}

// 可以审核的角色
export function canAudit(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}
