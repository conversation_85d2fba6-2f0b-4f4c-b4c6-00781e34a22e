import { prisma } from './prisma'
import { 
  SearchParams, 
  SearchResult, 
  SearchType, 
  SearchMode, 
  ProductWithRelations,
  SearchSuggestion 
} from '@/types/search'
import { Prisma } from '@prisma/client'

export class SearchService {
  
  // 主搜索方法
  async search(params: SearchParams): Promise<SearchResult> {
    const { query, type, mode, filters, sort, page, limit } = params
    
    // 构建基础查询条件
    const whereClause = this.buildWhereClause(query, type, mode, filters)
    
    // 构建排序条件
    const orderBy = this.buildOrderBy(sort)
    
    // 计算偏移量
    const skip = (page - 1) * limit
    
    // 执行查询
    const [products, total] = await Promise.all([
      this.getProducts(whereClause, orderBy, skip, limit),
      this.getProductCount(whereClause)
    ])
    
    // 获取可用筛选选项
    const availableFilters = await this.getAvailableFilters(whereClause)
    
    return {
      products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      filters: availableFilters
    }
  }
  
  // 构建查询条件
  private buildWhereClause(
    query: string, 
    type: SearchType, 
    mode: SearchMode, 
    filters: any
  ): Prisma.ProductWhereInput {
    const where: Prisma.ProductWhereInput = {
      status: 'APPROVED' // 只显示已审核的产品
    }
    
    // 添加搜索条件
    if (query.trim()) {
      const searchConditions = this.buildSearchConditions(query, type, mode)
      where.AND = [searchConditions]
    }
    
    // 添加筛选条件
    this.addFilters(where, filters)
    
    return where
  }
  
  // 构建搜索条件
  private buildSearchConditions(
    query: string, 
    type: SearchType, 
    mode: SearchMode
  ): Prisma.ProductWhereInput {
    const normalizedQuery = query.trim().toLowerCase()
    
    switch (type) {
      case SearchType.PRODUCT_NAME:
        return this.buildProductNameSearch(normalizedQuery, mode)
      
      case SearchType.ACTIVE_INGREDIENT:
        return this.buildActiveIngredientSearch(normalizedQuery, mode)
      
      case SearchType.CROP:
        return this.buildCropSearch(normalizedQuery, mode)
      
      case SearchType.PEST:
        return this.buildPestSearch(normalizedQuery, mode)
      
      case SearchType.BRAND:
        return this.buildBrandSearch(normalizedQuery, mode)
      
      default: // SearchType.ALL
        return this.buildGlobalSearch(normalizedQuery, mode)
    }
  }
  
  // 产品名称搜索
  private buildProductNameSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return { name: condition }
  }
  
  // 有效成分搜索
  private buildActiveIngredientSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return {
      activeIngredients: {
        some: {
          OR: [
            { activeIngredient: { name: condition } },
            { activeIngredient: { synonyms: { some: { synonym: condition } } } }
          ]
        }
      }
    }
  }
  
  // 作物搜索
  private buildCropSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return {
      crops: {
        some: {
          OR: [
            { crop: { name: condition } },
            { crop: { synonyms: { some: { synonym: condition } } } }
          ]
        }
      }
    }
  }
  
  // 病虫草害搜索
  private buildPestSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return {
      pests: {
        some: {
          OR: [
            { pest: { name: condition } },
            { pest: { synonyms: { some: { synonym: condition } } } }
          ]
        }
      }
    }
  }
  
  // 品牌搜索
  private buildBrandSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return {
      OR: [
        { brand: { name: condition } },
        { brand: { company: condition } }
      ]
    }
  }
  
  // 全局搜索
  private buildGlobalSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {
    const condition = mode === SearchMode.EXACT 
      ? { equals: query, mode: 'insensitive' as const }
      : { contains: query, mode: 'insensitive' as const }
    
    return {
      OR: [
        { name: condition },
        { description: condition },
        { brand: { name: condition } },
        { brand: { company: condition } },
        { activeIngredients: { some: { activeIngredient: { name: condition } } } },
        { crops: { some: { crop: { name: condition } } } },
        { pests: { some: { pest: { name: condition } } } }
      ]
    }
  }
  
  // 添加筛选条件
  private addFilters(where: Prisma.ProductWhereInput, filters: any): void {
    if (filters.priceMin !== undefined || filters.priceMax !== undefined) {
      where.price = {}
      if (filters.priceMin !== undefined) where.price.gte = filters.priceMin
      if (filters.priceMax !== undefined) where.price.lte = filters.priceMax
    }
    
    if (filters.activeIngredients?.length) {
      where.activeIngredients = {
        some: {
          activeIngredientId: { in: filters.activeIngredients }
        }
      }
    }
    
    if (filters.crops?.length) {
      where.crops = {
        some: {
          cropId: { in: filters.crops }
        }
      }
    }
    
    if (filters.pests?.length) {
      where.pests = {
        some: {
          pestId: { in: filters.pests }
        }
      }
    }
    
    if (filters.brands?.length) {
      where.brandId = { in: filters.brands }
    }
    
    if (filters.formulations?.length) {
      where.formulation = { in: filters.formulations }
    }
  }
  
  // 构建排序条件
  private buildOrderBy(sort: any): Prisma.ProductOrderByWithRelationInput[] {
    switch (sort.field) {
      case 'price':
        return [{ price: sort.order }]
      case 'createdAt':
        return [{ createdAt: sort.order }]
      case 'updatedAt':
        return [{ updatedAt: sort.order }]
      case 'name':
        return [{ name: sort.order }]
      default:
        return [{ createdAt: 'desc' }]
    }
  }
  
  // 获取产品列表
  private async getProducts(
    where: Prisma.ProductWhereInput,
    orderBy: Prisma.ProductOrderByWithRelationInput[],
    skip: number,
    take: number
  ): Promise<ProductWithRelations[]> {
    return prisma.product.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        brand: true,
        submitter: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        activeIngredients: {
          include: {
            activeIngredient: true
          }
        },
        crops: {
          include: {
            crop: true
          }
        },
        pests: {
          include: {
            pest: true
          }
        },
        recommendations: {
          take: 3,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                name: true,
                role: true
              }
            }
          }
        }
      }
    }) as ProductWithRelations[]
  }
  
  // 获取产品总数
  private async getProductCount(where: Prisma.ProductWhereInput): Promise<number> {
    return prisma.product.count({ where })
  }
  
  // 获取可用筛选选项
  private async getAvailableFilters(baseWhere: Prisma.ProductWhereInput) {
    // 这里可以实现获取当前搜索结果中可用的筛选选项
    // 为了简化，返回空对象
    return {
      availableActiveIngredients: [],
      availableCrops: [],
      availablePests: [],
      availableBrands: [],
      availableFormulations: [],
      priceRange: { min: 0, max: 0 }
    }
  }
  
  // 获取搜索建议
  async getSuggestions(query: string, limit: number = 10): Promise<SearchSuggestion[]> {
    const suggestions: SearchSuggestion[] = []
    
    if (query.length < 2) return suggestions
    
    const normalizedQuery = query.trim().toLowerCase()
    
    // 产品名称建议
    const products = await prisma.product.findMany({
      where: {
        name: { contains: normalizedQuery, mode: 'insensitive' },
        status: 'APPROVED'
      },
      select: { name: true },
      take: 3
    })
    
    products.forEach(product => {
      suggestions.push({
        type: SearchType.PRODUCT_NAME,
        value: product.name,
        label: product.name
      })
    })
    
    // 有效成分建议
    const ingredients = await prisma.activeIngredient.findMany({
      where: {
        OR: [
          { name: { contains: normalizedQuery, mode: 'insensitive' } },
          { synonyms: { some: { synonym: { contains: normalizedQuery, mode: 'insensitive' } } } }
        ]
      },
      select: { name: true },
      take: 3
    })
    
    ingredients.forEach(ingredient => {
      suggestions.push({
        type: SearchType.ACTIVE_INGREDIENT,
        value: ingredient.name,
        label: ingredient.name
      })
    })
    
    return suggestions.slice(0, limit)
  }
}

export const searchService = new SearchService()
