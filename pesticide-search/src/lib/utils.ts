import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 搜索相关工具函数
export function normalizeSearchTerm(term: string): string {
  return term.trim().toLowerCase()
}

export function createSearchQuery(term: string, fields: string[]): any {
  const normalizedTerm = normalizeSearchTerm(term)
  
  return {
    OR: fields.map(field => ({
      [field]: {
        contains: normalizedTerm,
        mode: 'insensitive'
      }
    }))
  }
}

// 价格格式化
export function formatPrice(price: number, currency: string = '¥'): string {
  return `${currency}${price.toFixed(2)}`
}

// 日期格式化
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 文件上传相关
export function validateImageFile(file: File): boolean {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
  const maxSize = 5 * 1024 * 1024 // 5MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize
}

// 分页工具
export interface PaginationParams {
  page: number
  limit: number
}

export function getPaginationParams(searchParams: URLSearchParams): PaginationParams {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '20')))
  
  return { page, limit }
}

export function getPaginationOffset(page: number, limit: number): number {
  return (page - 1) * limit
}
