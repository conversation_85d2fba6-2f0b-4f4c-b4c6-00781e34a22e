import { Header } from '@/components/layout/header'
import { SearchSection } from '@/components/search/search-section'
import { FeaturedProducts } from '@/components/products/featured-products'
import { RoleBasedContent } from '@/components/home/<USER>'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            专业的
            <span className="text-green-600">农药产品</span>
            搜索平台
          </h1>
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
            为农艺师、农药店和品牌方提供全面的产品信息、智能搜索和专业推荐服务
          </p>

          {/* Search Section */}
          <SearchSection />
        </div>
      </section>

      {/* Role-based Content */}
      <RoleBasedContent />

      {/* Featured Products */}
      <FeaturedProducts />

      {/* Features Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            平台特色
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">智能搜索</h3>
              <p className="text-gray-600">支持模糊匹配、精准匹配、同义词搜索等多种搜索模式</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">专业推荐</h3>
              <p className="text-gray-600">农艺师专业推荐，帮助用户选择最适合的产品</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">多角色服务</h3>
              <p className="text-gray-600">为农艺师、农药店、品牌方提供差异化服务</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-2xl font-bold mb-4">农药产品搜索平台</h3>
          <p className="text-gray-400 mb-8">专业、可靠、高效的农药产品信息服务</p>
          <div className="flex justify-center space-x-6">
            <a href="/about" className="text-gray-400 hover:text-white">关于我们</a>
            <a href="/contact" className="text-gray-400 hover:text-white">联系我们</a>
            <a href="/privacy" className="text-gray-400 hover:text-white">隐私政策</a>
            <a href="/terms" className="text-gray-400 hover:text-white">服务条款</a>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-gray-400">
            <p>&copy; 2024 农药产品搜索平台. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
