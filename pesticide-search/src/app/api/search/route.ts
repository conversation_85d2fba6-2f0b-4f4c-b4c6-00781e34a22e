import { NextRequest, NextResponse } from 'next/server'
import { searchService } from '@/lib/search'
import { SearchType, SearchMode } from '@/types/search'
import { z } from 'zod'

const searchSchema = z.object({
  query: z.string().min(1, '搜索关键词不能为空'),
  type: z.nativeEnum(SearchType).default(SearchType.ALL),
  mode: z.nativeEnum(SearchMode).default(SearchMode.FUZZY),
  filters: z.object({
    priceMin: z.number().optional(),
    priceMax: z.number().optional(),
    activeIngredients: z.array(z.string()).optional(),
    crops: z.array(z.string()).optional(),
    pests: z.array(z.string()).optional(),
    brands: z.array(z.string()).optional(),
    formulations: z.array(z.string()).optional(),
    userRoles: z.array(z.string()).optional(),
    status: z.array(z.string()).optional()
  }).default({}),
  sort: z.object({
    field: z.enum(['name', 'price', 'createdAt', 'updatedAt', 'relevance']).default('relevance'),
    order: z.enum(['asc', 'desc']).default('desc')
  }).default({ field: 'relevance', order: 'desc' }),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20)
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 解析查询参数
    const queryData = {
      query: searchParams.get('query') || '',
      type: searchParams.get('type') as SearchType || SearchType.ALL,
      mode: searchParams.get('mode') as SearchMode || SearchMode.FUZZY,
      filters: {
        priceMin: searchParams.get('priceMin') ? Number(searchParams.get('priceMin')) : undefined,
        priceMax: searchParams.get('priceMax') ? Number(searchParams.get('priceMax')) : undefined,
        activeIngredients: searchParams.get('activeIngredients')?.split(',').filter(Boolean) || [],
        crops: searchParams.get('crops')?.split(',').filter(Boolean) || [],
        pests: searchParams.get('pests')?.split(',').filter(Boolean) || [],
        brands: searchParams.get('brands')?.split(',').filter(Boolean) || [],
        formulations: searchParams.get('formulations')?.split(',').filter(Boolean) || []
      },
      sort: {
        field: (searchParams.get('sortField') as any) || 'relevance',
        order: (searchParams.get('sortOrder') as any) || 'desc'
      },
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20
    }

    // 验证数据
    const validatedData = searchSchema.parse(queryData)

    // 执行搜索
    const result = await searchService.search(validatedData)

    return NextResponse.json(result)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '搜索参数格式错误', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Search error:', error)
    return NextResponse.json(
      { error: '搜索失败，请稍后重试' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = searchSchema.parse(body)

    // 执行搜索
    const result = await searchService.search(validatedData)

    return NextResponse.json(result)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '搜索参数格式错误', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Search error:', error)
    return NextResponse.json(
      { error: '搜索失败，请稍后重试' },
      { status: 500 }
    )
  }
}
