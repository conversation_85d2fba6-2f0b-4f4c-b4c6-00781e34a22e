import { NextRequest, NextResponse } from 'next/server'
import { searchService } from '@/lib/search'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query') || ''
    const limit = Number(searchParams.get('limit')) || 10

    if (query.length < 2) {
      return NextResponse.json([])
    }

    const suggestions = await searchService.getSuggestions(query, limit)
    return NextResponse.json(suggestions)

  } catch (error) {
    console.error('Suggestions error:', error)
    return NextResponse.json(
      { error: '获取建议失败' },
      { status: 500 }
    )
  }
}
