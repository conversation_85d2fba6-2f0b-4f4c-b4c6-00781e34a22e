import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UserRole, UserStatus } from '@prisma/client'
import { z } from 'zod'

const registerSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  name: z.string().min(2, '姓名至少需要2个字符'),
  phone: z.string().optional(),
  role: z.nativeEnum(UserRole),
  companyName: z.string().optional(),
  description: z.string().optional(),
  license: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      )
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        name: validatedData.name,
        phone: validatedData.phone,
        role: validatedData.role,
        companyName: validatedData.companyName,
        description: validatedData.description,
        license: validatedData.license,
        status: UserStatus.PENDING // 默认待审核状态
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        companyName: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      message: '注册成功，请等待管理员审核',
      user
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '输入数据格式错误', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Registration error:', error)
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    )
  }
}
