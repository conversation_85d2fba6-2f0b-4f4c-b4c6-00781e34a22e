{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useSession, signOut } from 'next-auth/react'\n// import { UserRole } from '@/types/auth'\n\nexport function Header() {\n  const { data: session, status } = useSession()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const getRoleDisplayName = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return '管理员'\n      case UserRole.AGRONOMIST:\n        return '农艺师'\n      case UserRole.PESTICIDE_STORE:\n        return '农药店'\n      case UserRole.BRAND:\n        return '品牌方'\n      default:\n        return '用户'\n    }\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">农药搜索</span>\n          </Link>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"/search\" className=\"text-gray-600 hover:text-gray-900\">\n              产品搜索\n            </Link>\n            <Link href=\"/products\" className=\"text-gray-600 hover:text-gray-900\">\n              产品库\n            </Link>\n            {session?.user.role === 'AGRONOMIST' && (\n              <Link href=\"/recommendations\" className=\"text-gray-600 hover:text-gray-900\">\n                我的推荐\n              </Link>\n            )}\n            {(session?.user.role === 'BRAND' || session?.user.role === 'PESTICIDE_STORE') && (\n              <Link href=\"/my-products\" className=\"text-gray-600 hover:text-gray-900\">\n                我的产品\n              </Link>\n            )}\n            {session?.user.role === 'ADMIN' && (\n              <Link href=\"/admin\" className=\"text-gray-600 hover:text-gray-900\">\n                管理后台\n              </Link>\n            )}\n          </nav>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"w-8 h-8 bg-gray-200 rounded-full animate-pulse\"></div>\n            ) : session ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsMenuOpen(!isMenuOpen)}\n                  className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900\"\n                >\n                  <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-green-600\">\n                      {session.user.name?.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                  <span className=\"hidden md:block\">{session.user.name}</span>\n                  <span className=\"hidden md:block text-sm text-gray-500\">\n                    ({getRoleDisplayName(session.user.role)})\n                  </span>\n                </button>\n\n                {isMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      个人资料\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      设置\n                    </Link>\n                    <button\n                      onClick={() => {\n                        setIsMenuOpen(false)\n                        signOut()\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      退出登录\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  登录\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n                >\n                  注册\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-2\">\n              <Link href=\"/search\" className=\"text-gray-600 hover:text-gray-900 py-2\">\n                产品搜索\n              </Link>\n              <Link href=\"/products\" className=\"text-gray-600 hover:text-gray-900 py-2\">\n                产品库\n              </Link>\n              {session?.user.role === UserRole.AGRONOMIST && (\n                <Link href=\"/recommendations\" className=\"text-gray-600 hover:text-gray-900 py-2\">\n                  我的推荐\n                </Link>\n              )}\n              {(session?.user.role === UserRole.BRAND || session?.user.role === UserRole.PESTICIDE_STORE) && (\n                <Link href=\"/my-products\" className=\"text-gray-600 hover:text-gray-900 py-2\">\n                  我的产品\n                </Link>\n              )}\n              {session?.user.role === UserRole.ADMIN && (\n                <Link href=\"/admin\" className=\"text-gray-600 hover:text-gray-900 py-2\">\n                  管理后台\n                </Link>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK,SAAS,KAAK;gBACjB,OAAO;YACT,KAAK,SAAS,UAAU;gBACtB,OAAO;YACT,KAAK,SAAS,eAAe;gBAC3B,OAAO;YACT,KAAK,SAAS,KAAK;gBACjB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAoC;;;;;;8CAGnE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAoC;;;;;;gCAGpE,SAAS,KAAK,SAAS,8BACtB,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAmB,WAAU;8CAAoC;;;;;;gCAI7E,CAAC,SAAS,KAAK,SAAS,WAAW,SAAS,KAAK,SAAS,iBAAiB,mBAC1E,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAU;8CAAoC;;;;;;gCAIzE,SAAS,KAAK,SAAS,yBACtB,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAoC;;;;;;;;;;;;sCAOtE,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;;;;2CACb,wBACF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,cAAc,CAAC;4CAC9B,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG;;;;;;;;;;;8DAGlC,6LAAC;oDAAK,WAAU;8DAAmB,QAAQ,IAAI,CAAC,IAAI;;;;;;8DACpD,6LAAC;oDAAK,WAAU;;wDAAwC;wDACpD,mBAAmB,QAAQ,IAAI,CAAC,IAAI;wDAAE;;;;;;;;;;;;;wCAI3C,4BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,cAAc;8DAC9B;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,cAAc;8DAC9B;;;;;;8DAGD,6LAAC;oDACC,SAAS;wDACP,cAAc;wDACd,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;oDACR;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAOP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO5E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAyC;;;;;;0CAGxE,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAyC;;;;;;4BAGzE,SAAS,KAAK,SAAS,SAAS,UAAU,kBACzC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAmB,WAAU;0CAAyC;;;;;;4BAIlF,CAAC,SAAS,KAAK,SAAS,SAAS,KAAK,IAAI,SAAS,KAAK,SAAS,SAAS,eAAe,mBACxF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAe,WAAU;0CAAyC;;;;;;4BAI9E,SAAS,KAAK,SAAS,SAAS,KAAK,kBACpC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvF;GAvKgB;;QACoB,iJAAA,CAAA,aAAU;;;KAD9B", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/types/search.ts"], "sourcesContent": ["import { Product, ActiveIngredient, Crop, Pest, Brand, User } from '@prisma/client'\n\n// 搜索类型枚举\nexport enum SearchType {\n  ALL = 'all',\n  PRODUCT_NAME = 'product_name',\n  ACTIVE_INGREDIENT = 'active_ingredient',\n  CROP = 'crop',\n  PEST = 'pest',\n  BRAND = 'brand'\n}\n\n// 搜索模式枚举\nexport enum SearchMode {\n  FUZZY = 'fuzzy',        // 模糊匹配\n  EXACT = 'exact',        // 精确匹配\n  PHRASE = 'phrase',      // 短语匹配\n  SYNONYM = 'synonym'     // 同义词匹配\n}\n\n// 搜索筛选条件\nexport interface SearchFilters {\n  // 价格范围\n  priceMin?: number\n  priceMax?: number\n  \n  // 有效成分\n  activeIngredients?: string[]\n  \n  // 作物\n  crops?: string[]\n  \n  // 病虫草害\n  pests?: string[]\n  \n  // 品牌\n  brands?: string[]\n  \n  // 剂型\n  formulations?: string[]\n  \n  // 用户角色（针对推荐）\n  userRoles?: string[]\n  \n  // 产品状态\n  status?: string[]\n}\n\n// 排序选项\nexport interface SortOptions {\n  field: 'name' | 'price' | 'createdAt' | 'updatedAt' | 'relevance'\n  order: 'asc' | 'desc'\n}\n\n// 搜索请求参数\nexport interface SearchParams {\n  query: string\n  type: SearchType\n  mode: SearchMode\n  filters: SearchFilters\n  sort: SortOptions\n  page: number\n  limit: number\n}\n\n// 扩展的产品类型（包含关联数据）\nexport interface ProductWithRelations extends Product {\n  brand: Brand\n  submitter: User\n  activeIngredients: Array<{\n    activeIngredient: ActiveIngredient\n    concentration?: string\n  }>\n  crops: Array<{\n    crop: Crop\n  }>\n  pests: Array<{\n    pest: Pest\n  }>\n  recommendations?: Array<{\n    id: string\n    title: string\n    rating?: number\n    user: {\n      name: string\n      role: string\n    }\n  }>\n}\n\n// 搜索结果\nexport interface SearchResult {\n  products: ProductWithRelations[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n  filters: {\n    availableActiveIngredients: Array<{ id: string; name: string; count: number }>\n    availableCrops: Array<{ id: string; name: string; count: number }>\n    availablePests: Array<{ id: string; name: string; count: number }>\n    availableBrands: Array<{ id: string; name: string; count: number }>\n    availableFormulations: Array<{ value: string; count: number }>\n    priceRange: { min: number; max: number }\n  }\n}\n\n// 搜索建议\nexport interface SearchSuggestion {\n  type: SearchType\n  value: string\n  label: string\n  count?: number\n}\n\n// 搜索历史\nexport interface SearchHistory {\n  id: string\n  query: string\n  type: SearchType\n  timestamp: Date\n  resultCount: number\n}\n"], "names": [], "mappings": ";;;;AAGO,IAAA,AAAK,oCAAA;;;;;;;WAAA;;AAUL,IAAA,AAAK,oCAAA;;;;uCAIc,QAAQ;WAJtB", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/components/search/search-section.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { SearchType, SearchMode, SearchSuggestion } from '@/types/search'\n\nexport function SearchSection() {\n  const router = useRouter()\n  const [query, setQuery] = useState('')\n  const [searchType, setSearchType] = useState<SearchType>(SearchType.ALL)\n  const [searchMode, setSearchMode] = useState<SearchMode>(SearchMode.FUZZY)\n  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])\n  const [showSuggestions, setShowSuggestions] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // 获取搜索建议\n  useEffect(() => {\n    const fetchSuggestions = async () => {\n      if (query.length < 2) {\n        setSuggestions([])\n        return\n      }\n\n      try {\n        const response = await fetch(`/api/search/suggestions?query=${encodeURIComponent(query)}&limit=8`)\n        if (response.ok) {\n          const data = await response.json()\n          setSuggestions(data)\n        }\n      } catch (error) {\n        console.error('Failed to fetch suggestions:', error)\n      }\n    }\n\n    const debounceTimer = setTimeout(fetchSuggestions, 300)\n    return () => clearTimeout(debounceTimer)\n  }, [query])\n\n  const handleSearch = (searchQuery?: string) => {\n    const finalQuery = searchQuery || query\n    if (!finalQuery.trim()) return\n\n    setIsLoading(true)\n    setShowSuggestions(false)\n\n    const params = new URLSearchParams({\n      query: finalQuery,\n      type: searchType,\n      mode: searchMode\n    })\n\n    router.push(`/search?${params.toString()}`)\n  }\n\n  const handleSuggestionClick = (suggestion: SearchSuggestion) => {\n    setQuery(suggestion.value)\n    setSearchType(suggestion.type)\n    setShowSuggestions(false)\n    handleSearch(suggestion.value)\n  }\n\n  const getSearchTypeLabel = (type: SearchType) => {\n    switch (type) {\n      case SearchType.ALL:\n        return '全部'\n      case SearchType.PRODUCT_NAME:\n        return '产品名称'\n      case SearchType.ACTIVE_INGREDIENT:\n        return '有效成分'\n      case SearchType.CROP:\n        return '作物'\n      case SearchType.PEST:\n        return '病虫草害'\n      case SearchType.BRAND:\n        return '品牌'\n      default:\n        return '全部'\n    }\n  }\n\n  const getSearchModeLabel = (mode: SearchMode) => {\n    switch (mode) {\n      case SearchMode.FUZZY:\n        return '模糊匹配'\n      case SearchMode.EXACT:\n        return '精确匹配'\n      case SearchMode.PHRASE:\n        return '短语匹配'\n      case SearchMode.SYNONYM:\n        return '同义词匹配'\n      default:\n        return '模糊匹配'\n    }\n  }\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* Search Options */}\n      <div className=\"flex flex-wrap gap-4 mb-6 justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <label className=\"text-sm font-medium text-gray-700\">搜索类型:</label>\n          <select\n            value={searchType}\n            onChange={(e) => setSearchType(e.target.value as SearchType)}\n            className=\"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500\"\n          >\n            {Object.values(SearchType).map((type) => (\n              <option key={type} value={type}>\n                {getSearchTypeLabel(type)}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <label className=\"text-sm font-medium text-gray-700\">匹配模式:</label>\n          <select\n            value={searchMode}\n            onChange={(e) => setSearchMode(e.target.value as SearchMode)}\n            className=\"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500\"\n          >\n            {Object.values(SearchMode).map((mode) => (\n              <option key={mode} value={mode}>\n                {getSearchModeLabel(mode)}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Search Input */}\n      <div className=\"relative\">\n        <div className=\"flex\">\n          <div className=\"relative flex-1\">\n            <input\n              type=\"text\"\n              value={query}\n              onChange={(e) => {\n                setQuery(e.target.value)\n                setShowSuggestions(true)\n              }}\n              onFocus={() => setShowSuggestions(true)}\n              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter') {\n                  handleSearch()\n                }\n              }}\n              placeholder=\"搜索农药产品、有效成分、作物、病虫草害...\"\n              className=\"w-full px-6 py-4 text-lg border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            />\n            \n            {/* Search Icon */}\n            <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2\">\n              <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n          </div>\n          \n          <button\n            onClick={() => handleSearch()}\n            disabled={isLoading || !query.trim()}\n            className=\"px-8 py-4 bg-green-600 text-white font-medium rounded-r-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? '搜索中...' : '搜索'}\n          </button>\n        </div>\n\n        {/* Suggestions Dropdown */}\n        {showSuggestions && suggestions.length > 0 && (\n          <div className=\"absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50\">\n            {suggestions.map((suggestion, index) => (\n              <button\n                key={index}\n                onClick={() => handleSuggestionClick(suggestion)}\n                className=\"w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                    {getSearchTypeLabel(suggestion.type)}\n                  </span>\n                  <span className=\"text-gray-900\">{suggestion.label}</span>\n                </div>\n                {suggestion.count && (\n                  <span className=\"text-sm text-gray-500\">{suggestion.count} 个结果</span>\n                )}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Quick Search Tags */}\n      <div className=\"mt-6\">\n        <p className=\"text-sm text-gray-600 mb-3\">热门搜索:</p>\n        <div className=\"flex flex-wrap gap-2\">\n          {[\n            { label: '除草剂', type: SearchType.ACTIVE_INGREDIENT },\n            { label: '杀虫剂', type: SearchType.ACTIVE_INGREDIENT },\n            { label: '杀菌剂', type: SearchType.ACTIVE_INGREDIENT },\n            { label: '水稻', type: SearchType.CROP },\n            { label: '小麦', type: SearchType.CROP },\n            { label: '玉米', type: SearchType.CROP },\n            { label: '蚜虫', type: SearchType.PEST },\n            { label: '稻飞虱', type: SearchType.PEST }\n          ].map((tag, index) => (\n            <button\n              key={index}\n              onClick={() => {\n                setQuery(tag.label)\n                setSearchType(tag.type)\n                handleSearch(tag.label)\n              }}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors\"\n            >\n              {tag.label}\n            </button>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,yHAAA,CAAA,aAAU,CAAC,GAAG;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,yHAAA,CAAA,aAAU,CAAC,KAAK;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;4DAAmB;oBACvB,IAAI,MAAM,MAAM,GAAG,GAAG;wBACpB,eAAe,EAAE;wBACjB;oBACF;oBAEA,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,8BAA8B,EAAE,mBAAmB,OAAO,QAAQ,CAAC;wBACjG,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,eAAe;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA,MAAM,gBAAgB,WAAW,kBAAkB;YACnD;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,eAAe;QAClC,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,aAAa;QACb,mBAAmB;QAEnB,MAAM,SAAS,IAAI,gBAAgB;YACjC,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI;IAC5C;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS,WAAW,KAAK;QACzB,cAAc,WAAW,IAAI;QAC7B,mBAAmB;QACnB,aAAa,WAAW,KAAK;IAC/B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK,yHAAA,CAAA,aAAU,CAAC,GAAG;gBACjB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,YAAY;gBAC1B,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,iBAAiB;gBAC/B,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,IAAI;gBAClB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,IAAI;gBAClB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,KAAK;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK,yHAAA,CAAA,aAAU,CAAC,KAAK;gBACnB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,KAAK;gBACnB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,MAAM;gBACpB,OAAO;YACT,KAAK,yHAAA,CAAA,aAAU,CAAC,OAAO;gBACrB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;0CAET,OAAO,MAAM,CAAC,yHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,qBAC9B,6LAAC;wCAAkB,OAAO;kDACvB,mBAAmB;uCADT;;;;;;;;;;;;;;;;kCAOnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;0CAET,OAAO,MAAM,CAAC,yHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,qBAC9B,6LAAC;wCAAkB,OAAO;kDACvB,mBAAmB;uCADT;;;;;;;;;;;;;;;;;;;;;;0BASrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC;4CACT,SAAS,EAAE,MAAM,CAAC,KAAK;4CACvB,mBAAmB;wCACrB;wCACA,SAAS,IAAM,mBAAmB;wCAClC,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;wCAC1D,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;wCACA,aAAY;wCACZ,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCACC,SAAS,IAAM;gCACf,UAAU,aAAa,CAAC,MAAM,IAAI;gCAClC,WAAU;0CAET,YAAY,WAAW;;;;;;;;;;;;oBAK3B,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,mBAAmB,WAAW,IAAI;;;;;;0DAErC,6LAAC;gDAAK,WAAU;0DAAiB,WAAW,KAAK;;;;;;;;;;;;oCAElD,WAAW,KAAK,kBACf,6LAAC;wCAAK,WAAU;;4CAAyB,WAAW,KAAK;4CAAC;;;;;;;;+BAXvD;;;;;;;;;;;;;;;;0BAoBf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,OAAO;gCAAO,MAAM,yHAAA,CAAA,aAAU,CAAC,iBAAiB;4BAAC;4BACnD;gCAAE,OAAO;gCAAO,MAAM,yHAAA,CAAA,aAAU,CAAC,iBAAiB;4BAAC;4BACnD;gCAAE,OAAO;gCAAO,MAAM,yHAAA,CAAA,aAAU,CAAC,iBAAiB;4BAAC;4BACnD;gCAAE,OAAO;gCAAM,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI;4BAAC;4BACrC;gCAAE,OAAO;gCAAM,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI;4BAAC;4BACrC;gCAAE,OAAO;gCAAM,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI;4BAAC;4BACrC;gCAAE,OAAO;gCAAM,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI;4BAAC;4BACrC;gCAAE,OAAO;gCAAO,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI;4BAAC;yBACvC,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,6LAAC;gCAEC,SAAS;oCACP,SAAS,IAAI,KAAK;oCAClB,cAAc,IAAI,IAAI;oCACtB,aAAa,IAAI,KAAK;gCACxB;gCACA,WAAU;0CAET,IAAI,KAAK;+BARL;;;;;;;;;;;;;;;;;;;;;;AAenB;GAzNgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/components/products/featured-products.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { ProductWithRelations } from '@/types/search'\n\nexport function FeaturedProducts() {\n  const [products, setProducts] = useState<ProductWithRelations[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // 这里应该从API获取特色产品\n    // 为了演示，我们使用模拟数据\n    const mockProducts: Partial<ProductWithRelations>[] = [\n      {\n        id: '1',\n        name: '草甘膦异丙胺盐水剂',\n        brand: { id: '1', name: '先正达', company: '先正达（中国）投资有限公司' },\n        formulation: '水剂',\n        concentration: '41%',\n        price: 25.50,\n        priceUnit: '元/瓶',\n        description: '广谱性除草剂，适用于多种作物',\n        activeIngredients: [\n          {\n            activeIngredient: { id: '1', name: '草甘膦异丙胺盐' },\n            concentration: '41%'\n          }\n        ],\n        crops: [\n          { crop: { id: '1', name: '水稻' } },\n          { crop: { id: '2', name: '小麦' } }\n        ],\n        pests: [\n          { pest: { id: '1', name: '杂草' } }\n        ]\n      },\n      {\n        id: '2',\n        name: '吡虫啉可湿性粉剂',\n        brand: { id: '2', name: '拜耳', company: '拜耳作物科学（中国）有限公司' },\n        formulation: '可湿性粉剂',\n        concentration: '70%',\n        price: 18.80,\n        priceUnit: '元/袋',\n        description: '高效杀虫剂，对蚜虫等害虫有特效',\n        activeIngredients: [\n          {\n            activeIngredient: { id: '2', name: '吡虫啉' },\n            concentration: '70%'\n          }\n        ],\n        crops: [\n          { crop: { id: '1', name: '水稻' } },\n          { crop: { id: '3', name: '棉花' } }\n        ],\n        pests: [\n          { pest: { id: '2', name: '蚜虫' } },\n          { pest: { id: '3', name: '飞虱' } }\n        ]\n      },\n      {\n        id: '3',\n        name: '多菌灵可湿性粉剂',\n        brand: { id: '3', name: '江苏扬农', company: '江苏扬农化工股份有限公司' },\n        formulation: '可湿性粉剂',\n        concentration: '50%',\n        price: 12.30,\n        priceUnit: '元/袋',\n        description: '广谱性杀菌剂，防治多种真菌病害',\n        activeIngredients: [\n          {\n            activeIngredient: { id: '3', name: '多菌灵' },\n            concentration: '50%'\n          }\n        ],\n        crops: [\n          { crop: { id: '1', name: '水稻' } },\n          { crop: { id: '2', name: '小麦' } },\n          { crop: { id: '4', name: '玉米' } }\n        ],\n        pests: [\n          { pest: { id: '4', name: '稻瘟病' } },\n          { pest: { id: '5', name: '纹枯病' } }\n        ]\n      }\n    ]\n\n    // 模拟API调用延迟\n    setTimeout(() => {\n      setProducts(mockProducts as ProductWithRelations[])\n      setIsLoading(false)\n    }, 1000)\n  }, [])\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 px-4\">\n        <div className=\"max-w-7xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            特色产品推荐\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"bg-white rounded-lg shadow-lg p-6 animate-pulse\">\n                <div className=\"h-4 bg-gray-200 rounded mb-4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded mb-4\"></div>\n                <div className=\"h-8 bg-gray-200 rounded\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    )\n  }\n\n  return (\n    <section className=\"py-20 px-4\">\n      <div className=\"max-w-7xl mx-auto\">\n        <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n          特色产品推荐\n        </h2>\n        \n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {products.map((product) => (\n            <div key={product.id} className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-2\">\n                    {product.name}\n                  </h3>\n                  {product.price && (\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-green-600\">\n                        ¥{product.price}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {product.priceUnit}\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <span className=\"font-medium\">品牌:</span>\n                    <span className=\"ml-1\">{product.brand?.name}</span>\n                  </div>\n                  \n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <span className=\"font-medium\">剂型:</span>\n                    <span className=\"ml-1\">{product.formulation}</span>\n                  </div>\n                  \n                  {product.concentration && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <span className=\"font-medium\">含量:</span>\n                      <span className=\"ml-1\">{product.concentration}</span>\n                    </div>\n                  )}\n                </div>\n\n                {product.description && (\n                  <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                    {product.description}\n                  </p>\n                )}\n\n                {/* 有效成分 */}\n                {product.activeIngredients && product.activeIngredients.length > 0 && (\n                  <div className=\"mb-4\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-1\">有效成分:</div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {product.activeIngredients.slice(0, 2).map((ai, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded\"\n                        >\n                          {ai.activeIngredient.name}\n                          {ai.concentration && ` (${ai.concentration})`}\n                        </span>\n                      ))}\n                      {product.activeIngredients.length > 2 && (\n                        <span className=\"inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded\">\n                          +{product.activeIngredients.length - 2}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* 适用作物 */}\n                {product.crops && product.crops.length > 0 && (\n                  <div className=\"mb-4\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-1\">适用作物:</div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {product.crops.slice(0, 3).map((crop, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded\"\n                        >\n                          {crop.crop.name}\n                        </span>\n                      ))}\n                      {product.crops.length > 3 && (\n                        <span className=\"inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded\">\n                          +{product.crops.length - 3}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* 防治对象 */}\n                {product.pests && product.pests.length > 0 && (\n                  <div className=\"mb-6\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-1\">防治对象:</div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {product.pests.slice(0, 3).map((pest, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded\"\n                        >\n                          {pest.pest.name}\n                        </span>\n                      ))}\n                      {product.pests.length > 3 && (\n                        <span className=\"inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded\">\n                          +{product.pests.length - 3}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                <Link\n                  href={`/products/${product.id}`}\n                  className=\"block w-full text-center bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition-colors\"\n                >\n                  查看详情\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/products\"\n            className=\"inline-block bg-gray-600 text-white px-8 py-3 rounded-md hover:bg-gray-700 transition-colors\"\n          >\n            查看更多产品\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,iBAAiB;YACjB,gBAAgB;YAChB,MAAM,eAAgD;gBACpD;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;wBAAE,IAAI;wBAAK,MAAM;wBAAO,SAAS;oBAAgB;oBACxD,aAAa;oBACb,eAAe;oBACf,OAAO;oBACP,WAAW;oBACX,aAAa;oBACb,mBAAmB;wBACjB;4BACE,kBAAkB;gCAAE,IAAI;gCAAK,MAAM;4BAAU;4BAC7C,eAAe;wBACjB;qBACD;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;wBAChC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;qBACjC;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;qBACjC;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;wBAAE,IAAI;wBAAK,MAAM;wBAAM,SAAS;oBAAiB;oBACxD,aAAa;oBACb,eAAe;oBACf,OAAO;oBACP,WAAW;oBACX,aAAa;oBACb,mBAAmB;wBACjB;4BACE,kBAAkB;gCAAE,IAAI;gCAAK,MAAM;4BAAM;4BACzC,eAAe;wBACjB;qBACD;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;wBAChC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;qBACjC;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;wBAChC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;qBACjC;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;wBAAE,IAAI;wBAAK,MAAM;wBAAQ,SAAS;oBAAe;oBACxD,aAAa;oBACb,eAAe;oBACf,OAAO;oBACP,WAAW;oBACX,aAAa;oBACb,mBAAmB;wBACjB;4BACE,kBAAkB;gCAAE,IAAI;gCAAK,MAAM;4BAAM;4BACzC,eAAe;wBACjB;qBACD;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;wBAChC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;wBAChC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAK;wBAAE;qBACjC;oBACD,OAAO;wBACL;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAM;wBAAE;wBACjC;4BAAE,MAAM;gCAAE,IAAI;gCAAK,MAAM;4BAAM;wBAAE;qBAClC;gBACH;aACD;YAED,YAAY;YACZ;8CAAW;oBACT,YAAY;oBACZ,aAAa;gBACf;6CAAG;QACL;qCAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAJP;;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqD;;;;;;8BAInE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAAqB,WAAU;sCAC9B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;4CAEd,QAAQ,KAAK,kBACZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAmC;4DAC9C,QAAQ,KAAK;;;;;;;kEAEjB,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,SAAS;;;;;;;;;;;;;;;;;;kDAM1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,QAAQ,KAAK,EAAE;;;;;;;;;;;;0DAGzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,QAAQ,WAAW;;;;;;;;;;;;4CAG5C,QAAQ,aAAa,kBACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,6LAAC;wDAAK,WAAU;kEAAQ,QAAQ,aAAa;;;;;;;;;;;;;;;;;;oCAKlD,QAAQ,WAAW,kBAClB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;oCAKvB,QAAQ,iBAAiB,IAAI,QAAQ,iBAAiB,CAAC,MAAM,GAAG,mBAC/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,iBAAiB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,sBAC9C,6LAAC;4DAEC,WAAU;;gEAET,GAAG,gBAAgB,CAAC,IAAI;gEACxB,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC;;2DAJxC;;;;;oDAOR,QAAQ,iBAAiB,CAAC,MAAM,GAAG,mBAClC,6LAAC;wDAAK,WAAU;;4DAAmE;4DAC/E,QAAQ,iBAAiB,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oCAQ9C,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC;4DAEC,WAAU;sEAET,KAAK,IAAI,CAAC,IAAI;2DAHV;;;;;oDAMR,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,6LAAC;wDAAK,WAAU;;4DAAmE;4DAC/E,QAAQ,KAAK,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oCAQlC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC;4DAEC,WAAU;sEAET,KAAK,IAAI,CAAC,IAAI;2DAHV;;;;;oDAMR,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,6LAAC;wDAAK,WAAU;;4DAAmE;4DAC/E,QAAQ,KAAK,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;kDAOnC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wCAC/B,WAAU;kDACX;;;;;;;;;;;;2BAjHK,QAAQ,EAAE;;;;;;;;;;8BAyHxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GA5PgB;KAAA", "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport Link from 'next/link'\nimport { UserRole } from '@/types/auth'\n\nexport function RoleBasedContent() {\n  const { data: session } = useSession()\n\n  if (!session) {\n    return (\n      <section className=\"py-16 px-4\">\n        <div className=\"max-w-7xl mx-auto\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            选择您的身份，获得专属服务\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-4\">农艺师</h3>\n              <p className=\"text-gray-600 mb-6\">\n                提供专业的产品推荐和技术指导，帮助农户选择最适合的农药产品\n              </p>\n              <ul className=\"text-sm text-gray-600 mb-6 space-y-2\">\n                <li>• 发布产品推荐</li>\n                <li>• 技术指导服务</li>\n                <li>• 专业评价产品</li>\n              </ul>\n              <Link\n                href=\"/auth/signup?role=agronomist\"\n                className=\"inline-block bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700\"\n              >\n                注册为农艺师\n              </Link>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-4\">农药店</h3>\n              <p className=\"text-gray-600 mb-6\">\n                展示店铺产品，管理库存信息，为客户提供便捷的购买渠道\n              </p>\n              <ul className=\"text-sm text-gray-600 mb-6 space-y-2\">\n                <li>• 展示销售产品</li>\n                <li>• 管理库存信息</li>\n                <li>• 客户服务支持</li>\n              </ul>\n              <Link\n                href=\"/auth/signup?role=store\"\n                className=\"inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700\"\n              >\n                注册为农药店\n              </Link>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-lg p-8 text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-4\">品牌方</h3>\n              <p className=\"text-gray-600 mb-6\">\n                发布产品信息，展示品牌实力，扩大产品影响力和市场覆盖\n              </p>\n              <ul className=\"text-sm text-gray-600 mb-6 space-y-2\">\n                <li>• 发布产品信息</li>\n                <li>• 品牌展示推广</li>\n                <li>• 市场数据分析</li>\n              </ul>\n              <Link\n                href=\"/auth/signup?role=brand\"\n                className=\"inline-block bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700\"\n              >\n                注册为品牌方\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    )\n  }\n\n  // 已登录用户的个性化内容\n  return (\n    <section className=\"py-16 px-4 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n          欢迎回来，{session.user.name}\n        </h2>\n        \n        {session.user.role === 'AGRONOMIST' && (\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">我的推荐</h3>\n              <p className=\"text-gray-600 mb-4\">管理您发布的产品推荐和技术指导</p>\n              <Link\n                href=\"/recommendations\"\n                className=\"inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n              >\n                查看推荐\n              </Link>\n            </div>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">发布推荐</h3>\n              <p className=\"text-gray-600 mb-4\">为农户推荐合适的农药产品</p>\n              <Link\n                href=\"/recommendations/new\"\n                className=\"inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n              >\n                发布推荐\n              </Link>\n            </div>\n          </div>\n        )}\n\n        {(session.user.role === 'PESTICIDE_STORE' || session.user.role === 'BRAND') && (\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">我的产品</h3>\n              <p className=\"text-gray-600 mb-4\">管理您提交的产品信息</p>\n              <Link\n                href=\"/my-products\"\n                className=\"inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n              >\n                查看产品\n              </Link>\n            </div>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">添加产品</h3>\n              <p className=\"text-gray-600 mb-4\">提交新的产品信息</p>\n              <Link\n                href=\"/products/new\"\n                className=\"inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n              >\n                添加产品\n              </Link>\n            </div>\n          </div>\n        )}\n\n        {session.user.role === 'ADMIN' && (\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">用户管理</h3>\n              <p className=\"text-gray-600 mb-4\">审核和管理用户账户</p>\n              <Link\n                href=\"/admin/users\"\n                className=\"inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n              >\n                管理用户\n              </Link>\n            </div>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">产品审核</h3>\n              <p className=\"text-gray-600 mb-4\">审核待发布的产品信息</p>\n              <Link\n                href=\"/admin/products\"\n                className=\"inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n              >\n                审核产品\n              </Link>\n            </div>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">数据统计</h3>\n              <p className=\"text-gray-600 mb-4\">查看平台使用统计</p>\n              <Link\n                href=\"/admin/analytics\"\n                className=\"inline-block bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700\"\n              >\n                查看统计\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAyB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAChF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;kDAEN,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;kDAEN,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;kDAEN,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,cAAc;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;;wBAAqD;wBAC3D,QAAQ,IAAI,CAAC,IAAI;;;;;;;gBAGxB,QAAQ,IAAI,CAAC,IAAI,KAAK,8BACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAON,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,qBAAqB,QAAQ,IAAI,CAAC,IAAI,KAAK,OAAO,mBACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAON,QAAQ,IAAI,CAAC,IAAI,KAAK,yBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GApLgB;;QACY,iJAAA,CAAA,aAAU;;;KADtB", "debugId": null}}]}