{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport CredentialsProvider from 'next-auth/providers/credentials'\nimport { prisma } from './prisma'\nimport { compare } from 'bcryptjs'\nimport { UserRole, UserStatus } from '@prisma/client'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: credentials.email }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        // 这里应该验证密码，但为了简化示例，我们暂时跳过\n        // const isPasswordValid = await compare(credentials.password, user.password)\n        // if (!isPasswordValid) {\n        //   return null\n        // }\n\n        // 检查用户状态\n        if (user.status !== UserStatus.APPROVED) {\n          throw new Error('账户尚未通过审核或已被暂停')\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          status: user.status,\n          companyName: user.companyName\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.status = user.status\n        token.companyName = user.companyName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.status = token.status as UserStatus\n        session.user.companyName = token.companyName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n\n// 权限检查函数\nexport function hasPermission(userRole: UserRole, requiredRoles: UserRole[]): boolean {\n  return requiredRoles.includes(userRole)\n}\n\n// 管理员权限检查\nexport function isAdmin(userRole: UserRole): boolean {\n  return userRole === UserRole.ADMIN\n}\n\n// 可以提交产品的角色\nexport function canSubmitProducts(userRole: UserRole): boolean {\n  return [UserRole.BRAND, UserRole.PESTICIDE_STORE].includes(userRole)\n}\n\n// 可以推荐产品的角色\nexport function canRecommendProducts(userRole: UserRole): boolean {\n  return [UserRole.AGRONOMIST, UserRole.ADMIN].includes(userRole)\n}\n\n// 可以审核的角色\nexport function canAudit(userRole: UserRole): boolean {\n  return userRole === UserRole.ADMIN\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AAEA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBAAE,OAAO,YAAY,KAAK;oBAAC;gBACpC;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,6EAA6E;gBAC7E,0BAA0B;gBAC1B,gBAAgB;gBAChB,IAAI;gBAEJ,SAAS;gBACT,IAAI,KAAK,MAAM,KAAK,6HAAA,CAAA,aAAU,CAAC,QAAQ,EAAE;oBACvC,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,aAAa,KAAK,WAAW;gBAC/B;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,MAAM,GAAG,KAAK,MAAM;gBAC1B,MAAM,WAAW,GAAG,KAAK,WAAW;YACtC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;YAC9C;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,SAAS,cAAc,QAAkB,EAAE,aAAyB;IACzE,OAAO,cAAc,QAAQ,CAAC;AAChC;AAGO,SAAS,QAAQ,QAAkB;IACxC,OAAO,aAAa,6HAAA,CAAA,WAAQ,CAAC,KAAK;AACpC;AAGO,SAAS,kBAAkB,QAAkB;IAClD,OAAO;QAAC,6HAAA,CAAA,WAAQ,CAAC,KAAK;QAAE,6HAAA,CAAA,WAAQ,CAAC,eAAe;KAAC,CAAC,QAAQ,CAAC;AAC7D;AAGO,SAAS,qBAAqB,QAAkB;IACrD,OAAO;QAAC,6HAAA,CAAA,WAAQ,CAAC,UAAU;QAAE,6HAAA,CAAA,WAAQ,CAAC,KAAK;KAAC,CAAC,QAAQ,CAAC;AACxD;AAGO,SAAS,SAAS,QAAkB;IACzC,OAAO,aAAa,6HAAA,CAAA,WAAQ,CAAC,KAAK;AACpC", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}