{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/types/search.ts"], "sourcesContent": ["import { Product, ActiveIngredient, Crop, Pest, Brand, User } from '@prisma/client'\n\n// 搜索类型枚举\nexport enum SearchType {\n  ALL = 'all',\n  PRODUCT_NAME = 'product_name',\n  ACTIVE_INGREDIENT = 'active_ingredient',\n  CROP = 'crop',\n  PEST = 'pest',\n  BRAND = 'brand'\n}\n\n// 搜索模式枚举\nexport enum SearchMode {\n  FUZZY = 'fuzzy',        // 模糊匹配\n  EXACT = 'exact',        // 精确匹配\n  PHRASE = 'phrase',      // 短语匹配\n  SYNONYM = 'synonym'     // 同义词匹配\n}\n\n// 搜索筛选条件\nexport interface SearchFilters {\n  // 价格范围\n  priceMin?: number\n  priceMax?: number\n  \n  // 有效成分\n  activeIngredients?: string[]\n  \n  // 作物\n  crops?: string[]\n  \n  // 病虫草害\n  pests?: string[]\n  \n  // 品牌\n  brands?: string[]\n  \n  // 剂型\n  formulations?: string[]\n  \n  // 用户角色（针对推荐）\n  userRoles?: string[]\n  \n  // 产品状态\n  status?: string[]\n}\n\n// 排序选项\nexport interface SortOptions {\n  field: 'name' | 'price' | 'createdAt' | 'updatedAt' | 'relevance'\n  order: 'asc' | 'desc'\n}\n\n// 搜索请求参数\nexport interface SearchParams {\n  query: string\n  type: SearchType\n  mode: SearchMode\n  filters: SearchFilters\n  sort: SortOptions\n  page: number\n  limit: number\n}\n\n// 扩展的产品类型（包含关联数据）\nexport interface ProductWithRelations extends Product {\n  brand: Brand\n  submitter: User\n  activeIngredients: Array<{\n    activeIngredient: ActiveIngredient\n    concentration?: string\n  }>\n  crops: Array<{\n    crop: Crop\n  }>\n  pests: Array<{\n    pest: Pest\n  }>\n  recommendations?: Array<{\n    id: string\n    title: string\n    rating?: number\n    user: {\n      name: string\n      role: string\n    }\n  }>\n}\n\n// 搜索结果\nexport interface SearchResult {\n  products: ProductWithRelations[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n  filters: {\n    availableActiveIngredients: Array<{ id: string; name: string; count: number }>\n    availableCrops: Array<{ id: string; name: string; count: number }>\n    availablePests: Array<{ id: string; name: string; count: number }>\n    availableBrands: Array<{ id: string; name: string; count: number }>\n    availableFormulations: Array<{ value: string; count: number }>\n    priceRange: { min: number; max: number }\n  }\n}\n\n// 搜索建议\nexport interface SearchSuggestion {\n  type: SearchType\n  value: string\n  label: string\n  count?: number\n}\n\n// 搜索历史\nexport interface SearchHistory {\n  id: string\n  query: string\n  type: SearchType\n  timestamp: Date\n  resultCount: number\n}\n"], "names": [], "mappings": ";;;;AAGO,IAAA,AAAK,oCAAA;;;;;;;WAAA;;AAUL,IAAA,AAAK,oCAAA;;;;uCAIc,QAAQ;WAJtB", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/lib/search.ts"], "sourcesContent": ["import { prisma } from './prisma'\nimport { \n  SearchParams, \n  SearchResult, \n  SearchType, \n  SearchMode, \n  ProductWithRelations,\n  SearchSuggestion \n} from '@/types/search'\nimport { Prisma } from '@prisma/client'\n\nexport class SearchService {\n  \n  // 主搜索方法\n  async search(params: SearchParams): Promise<SearchResult> {\n    const { query, type, mode, filters, sort, page, limit } = params\n    \n    // 构建基础查询条件\n    const whereClause = this.buildWhereClause(query, type, mode, filters)\n    \n    // 构建排序条件\n    const orderBy = this.buildOrderBy(sort)\n    \n    // 计算偏移量\n    const skip = (page - 1) * limit\n    \n    // 执行查询\n    const [products, total] = await Promise.all([\n      this.getProducts(whereClause, orderBy, skip, limit),\n      this.getProductCount(whereClause)\n    ])\n    \n    // 获取可用筛选选项\n    const availableFilters = await this.getAvailableFilters(whereClause)\n    \n    return {\n      products,\n      total,\n      page,\n      limit,\n      totalPages: Math.ceil(total / limit),\n      filters: availableFilters\n    }\n  }\n  \n  // 构建查询条件\n  private buildWhereClause(\n    query: string, \n    type: SearchType, \n    mode: SearchMode, \n    filters: any\n  ): Prisma.ProductWhereInput {\n    const where: Prisma.ProductWhereInput = {\n      status: 'APPROVED' // 只显示已审核的产品\n    }\n    \n    // 添加搜索条件\n    if (query.trim()) {\n      const searchConditions = this.buildSearchConditions(query, type, mode)\n      where.AND = [searchConditions]\n    }\n    \n    // 添加筛选条件\n    this.addFilters(where, filters)\n    \n    return where\n  }\n  \n  // 构建搜索条件\n  private buildSearchConditions(\n    query: string, \n    type: SearchType, \n    mode: SearchMode\n  ): Prisma.ProductWhereInput {\n    const normalizedQuery = query.trim().toLowerCase()\n    \n    switch (type) {\n      case SearchType.PRODUCT_NAME:\n        return this.buildProductNameSearch(normalizedQuery, mode)\n      \n      case SearchType.ACTIVE_INGREDIENT:\n        return this.buildActiveIngredientSearch(normalizedQuery, mode)\n      \n      case SearchType.CROP:\n        return this.buildCropSearch(normalizedQuery, mode)\n      \n      case SearchType.PEST:\n        return this.buildPestSearch(normalizedQuery, mode)\n      \n      case SearchType.BRAND:\n        return this.buildBrandSearch(normalizedQuery, mode)\n      \n      default: // SearchType.ALL\n        return this.buildGlobalSearch(normalizedQuery, mode)\n    }\n  }\n  \n  // 产品名称搜索\n  private buildProductNameSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return { name: condition }\n  }\n  \n  // 有效成分搜索\n  private buildActiveIngredientSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return {\n      activeIngredients: {\n        some: {\n          OR: [\n            { activeIngredient: { name: condition } },\n            { activeIngredient: { synonyms: { some: { synonym: condition } } } }\n          ]\n        }\n      }\n    }\n  }\n  \n  // 作物搜索\n  private buildCropSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return {\n      crops: {\n        some: {\n          OR: [\n            { crop: { name: condition } },\n            { crop: { synonyms: { some: { synonym: condition } } } }\n          ]\n        }\n      }\n    }\n  }\n  \n  // 病虫草害搜索\n  private buildPestSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return {\n      pests: {\n        some: {\n          OR: [\n            { pest: { name: condition } },\n            { pest: { synonyms: { some: { synonym: condition } } } }\n          ]\n        }\n      }\n    }\n  }\n  \n  // 品牌搜索\n  private buildBrandSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return {\n      OR: [\n        { brand: { name: condition } },\n        { brand: { company: condition } }\n      ]\n    }\n  }\n  \n  // 全局搜索\n  private buildGlobalSearch(query: string, mode: SearchMode): Prisma.ProductWhereInput {\n    const condition = mode === SearchMode.EXACT \n      ? { equals: query, mode: 'insensitive' as const }\n      : { contains: query, mode: 'insensitive' as const }\n    \n    return {\n      OR: [\n        { name: condition },\n        { description: condition },\n        { brand: { name: condition } },\n        { brand: { company: condition } },\n        { activeIngredients: { some: { activeIngredient: { name: condition } } } },\n        { crops: { some: { crop: { name: condition } } } },\n        { pests: { some: { pest: { name: condition } } } }\n      ]\n    }\n  }\n  \n  // 添加筛选条件\n  private addFilters(where: Prisma.ProductWhereInput, filters: any): void {\n    if (filters.priceMin !== undefined || filters.priceMax !== undefined) {\n      where.price = {}\n      if (filters.priceMin !== undefined) where.price.gte = filters.priceMin\n      if (filters.priceMax !== undefined) where.price.lte = filters.priceMax\n    }\n    \n    if (filters.activeIngredients?.length) {\n      where.activeIngredients = {\n        some: {\n          activeIngredientId: { in: filters.activeIngredients }\n        }\n      }\n    }\n    \n    if (filters.crops?.length) {\n      where.crops = {\n        some: {\n          cropId: { in: filters.crops }\n        }\n      }\n    }\n    \n    if (filters.pests?.length) {\n      where.pests = {\n        some: {\n          pestId: { in: filters.pests }\n        }\n      }\n    }\n    \n    if (filters.brands?.length) {\n      where.brandId = { in: filters.brands }\n    }\n    \n    if (filters.formulations?.length) {\n      where.formulation = { in: filters.formulations }\n    }\n  }\n  \n  // 构建排序条件\n  private buildOrderBy(sort: any): Prisma.ProductOrderByWithRelationInput[] {\n    switch (sort.field) {\n      case 'price':\n        return [{ price: sort.order }]\n      case 'createdAt':\n        return [{ createdAt: sort.order }]\n      case 'updatedAt':\n        return [{ updatedAt: sort.order }]\n      case 'name':\n        return [{ name: sort.order }]\n      default:\n        return [{ createdAt: 'desc' }]\n    }\n  }\n  \n  // 获取产品列表\n  private async getProducts(\n    where: Prisma.ProductWhereInput,\n    orderBy: Prisma.ProductOrderByWithRelationInput[],\n    skip: number,\n    take: number\n  ): Promise<ProductWithRelations[]> {\n    return prisma.product.findMany({\n      where,\n      orderBy,\n      skip,\n      take,\n      include: {\n        brand: true,\n        submitter: {\n          select: {\n            id: true,\n            name: true,\n            role: true\n          }\n        },\n        activeIngredients: {\n          include: {\n            activeIngredient: true\n          }\n        },\n        crops: {\n          include: {\n            crop: true\n          }\n        },\n        pests: {\n          include: {\n            pest: true\n          }\n        },\n        recommendations: {\n          take: 3,\n          orderBy: { createdAt: 'desc' },\n          include: {\n            user: {\n              select: {\n                name: true,\n                role: true\n              }\n            }\n          }\n        }\n      }\n    }) as ProductWithRelations[]\n  }\n  \n  // 获取产品总数\n  private async getProductCount(where: Prisma.ProductWhereInput): Promise<number> {\n    return prisma.product.count({ where })\n  }\n  \n  // 获取可用筛选选项\n  private async getAvailableFilters(baseWhere: Prisma.ProductWhereInput) {\n    // 这里可以实现获取当前搜索结果中可用的筛选选项\n    // 为了简化，返回空对象\n    return {\n      availableActiveIngredients: [],\n      availableCrops: [],\n      availablePests: [],\n      availableBrands: [],\n      availableFormulations: [],\n      priceRange: { min: 0, max: 0 }\n    }\n  }\n  \n  // 获取搜索建议\n  async getSuggestions(query: string, limit: number = 10): Promise<SearchSuggestion[]> {\n    const suggestions: SearchSuggestion[] = []\n    \n    if (query.length < 2) return suggestions\n    \n    const normalizedQuery = query.trim().toLowerCase()\n    \n    // 产品名称建议\n    const products = await prisma.product.findMany({\n      where: {\n        name: { contains: normalizedQuery, mode: 'insensitive' },\n        status: 'APPROVED'\n      },\n      select: { name: true },\n      take: 3\n    })\n    \n    products.forEach(product => {\n      suggestions.push({\n        type: SearchType.PRODUCT_NAME,\n        value: product.name,\n        label: product.name\n      })\n    })\n    \n    // 有效成分建议\n    const ingredients = await prisma.activeIngredient.findMany({\n      where: {\n        OR: [\n          { name: { contains: normalizedQuery, mode: 'insensitive' } },\n          { synonyms: { some: { synonym: { contains: normalizedQuery, mode: 'insensitive' } } } }\n        ]\n      },\n      select: { name: true },\n      take: 3\n    })\n    \n    ingredients.forEach(ingredient => {\n      suggestions.push({\n        type: SearchType.ACTIVE_INGREDIENT,\n        value: ingredient.name,\n        label: ingredient.name\n      })\n    })\n    \n    return suggestions.slice(0, limit)\n  }\n}\n\nexport const searchService = new SearchService()\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAUO,MAAM;IAEX,QAAQ;IACR,MAAM,OAAO,MAAoB,EAAyB;QACxD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;QAE1D,WAAW;QACX,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC,OAAO,MAAM,MAAM;QAE7D,SAAS;QACT,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC;QAElC,QAAQ;QACR,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,OAAO;QACP,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,aAAa,SAAS,MAAM;YAC7C,IAAI,CAAC,eAAe,CAAC;SACtB;QAED,WAAW;QACX,MAAM,mBAAmB,MAAM,IAAI,CAAC,mBAAmB,CAAC;QAExD,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY,KAAK,IAAI,CAAC,QAAQ;YAC9B,SAAS;QACX;IACF;IAEA,SAAS;IACD,iBACN,KAAa,EACb,IAAgB,EAChB,IAAgB,EAChB,OAAY,EACc;QAC1B,MAAM,QAAkC;YACtC,QAAQ,WAAW,YAAY;QACjC;QAEA,SAAS;QACT,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,mBAAmB,IAAI,CAAC,qBAAqB,CAAC,OAAO,MAAM;YACjE,MAAM,GAAG,GAAG;gBAAC;aAAiB;QAChC;QAEA,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,OAAO;QAEvB,OAAO;IACT;IAEA,SAAS;IACD,sBACN,KAAa,EACb,IAAgB,EAChB,IAAgB,EACU;QAC1B,MAAM,kBAAkB,MAAM,IAAI,GAAG,WAAW;QAEhD,OAAQ;YACN,KAAK,wHAAA,CAAA,aAAU,CAAC,YAAY;gBAC1B,OAAO,IAAI,CAAC,sBAAsB,CAAC,iBAAiB;YAEtD,KAAK,wHAAA,CAAA,aAAU,CAAC,iBAAiB;gBAC/B,OAAO,IAAI,CAAC,2BAA2B,CAAC,iBAAiB;YAE3D,KAAK,wHAAA,CAAA,aAAU,CAAC,IAAI;gBAClB,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAE/C,KAAK,wHAAA,CAAA,aAAU,CAAC,IAAI;gBAClB,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YAE/C,KAAK,wHAAA,CAAA,aAAU,CAAC,KAAK;gBACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;YAEhD;gBACE,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;QACnD;IACF;IAEA,SAAS;IACD,uBAAuB,KAAa,EAAE,IAAgB,EAA4B;QACxF,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YAAE,MAAM;QAAU;IAC3B;IAEA,SAAS;IACD,4BAA4B,KAAa,EAAE,IAAgB,EAA4B;QAC7F,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YACL,mBAAmB;gBACjB,MAAM;oBACJ,IAAI;wBACF;4BAAE,kBAAkB;gCAAE,MAAM;4BAAU;wBAAE;wBACxC;4BAAE,kBAAkB;gCAAE,UAAU;oCAAE,MAAM;wCAAE,SAAS;oCAAU;gCAAE;4BAAE;wBAAE;qBACpE;gBACH;YACF;QACF;IACF;IAEA,OAAO;IACC,gBAAgB,KAAa,EAAE,IAAgB,EAA4B;QACjF,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YACL,OAAO;gBACL,MAAM;oBACJ,IAAI;wBACF;4BAAE,MAAM;gCAAE,MAAM;4BAAU;wBAAE;wBAC5B;4BAAE,MAAM;gCAAE,UAAU;oCAAE,MAAM;wCAAE,SAAS;oCAAU;gCAAE;4BAAE;wBAAE;qBACxD;gBACH;YACF;QACF;IACF;IAEA,SAAS;IACD,gBAAgB,KAAa,EAAE,IAAgB,EAA4B;QACjF,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YACL,OAAO;gBACL,MAAM;oBACJ,IAAI;wBACF;4BAAE,MAAM;gCAAE,MAAM;4BAAU;wBAAE;wBAC5B;4BAAE,MAAM;gCAAE,UAAU;oCAAE,MAAM;wCAAE,SAAS;oCAAU;gCAAE;4BAAE;wBAAE;qBACxD;gBACH;YACF;QACF;IACF;IAEA,OAAO;IACC,iBAAiB,KAAa,EAAE,IAAgB,EAA4B;QAClF,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YACL,IAAI;gBACF;oBAAE,OAAO;wBAAE,MAAM;oBAAU;gBAAE;gBAC7B;oBAAE,OAAO;wBAAE,SAAS;oBAAU;gBAAE;aACjC;QACH;IACF;IAEA,OAAO;IACC,kBAAkB,KAAa,EAAE,IAAgB,EAA4B;QACnF,MAAM,YAAY,SAAS,wHAAA,CAAA,aAAU,CAAC,KAAK,GACvC;YAAE,QAAQ;YAAO,MAAM;QAAuB,IAC9C;YAAE,UAAU;YAAO,MAAM;QAAuB;QAEpD,OAAO;YACL,IAAI;gBACF;oBAAE,MAAM;gBAAU;gBAClB;oBAAE,aAAa;gBAAU;gBACzB;oBAAE,OAAO;wBAAE,MAAM;oBAAU;gBAAE;gBAC7B;oBAAE,OAAO;wBAAE,SAAS;oBAAU;gBAAE;gBAChC;oBAAE,mBAAmB;wBAAE,MAAM;4BAAE,kBAAkB;gCAAE,MAAM;4BAAU;wBAAE;oBAAE;gBAAE;gBACzE;oBAAE,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,MAAM;4BAAU;wBAAE;oBAAE;gBAAE;gBACjD;oBAAE,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,MAAM;4BAAU;wBAAE;oBAAE;gBAAE;aAClD;QACH;IACF;IAEA,SAAS;IACD,WAAW,KAA+B,EAAE,OAAY,EAAQ;QACtE,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,WAAW;YACpE,MAAM,KAAK,GAAG,CAAC;YACf,IAAI,QAAQ,QAAQ,KAAK,WAAW,MAAM,KAAK,CAAC,GAAG,GAAG,QAAQ,QAAQ;YACtE,IAAI,QAAQ,QAAQ,KAAK,WAAW,MAAM,KAAK,CAAC,GAAG,GAAG,QAAQ,QAAQ;QACxE;QAEA,IAAI,QAAQ,iBAAiB,EAAE,QAAQ;YACrC,MAAM,iBAAiB,GAAG;gBACxB,MAAM;oBACJ,oBAAoB;wBAAE,IAAI,QAAQ,iBAAiB;oBAAC;gBACtD;YACF;QACF;QAEA,IAAI,QAAQ,KAAK,EAAE,QAAQ;YACzB,MAAM,KAAK,GAAG;gBACZ,MAAM;oBACJ,QAAQ;wBAAE,IAAI,QAAQ,KAAK;oBAAC;gBAC9B;YACF;QACF;QAEA,IAAI,QAAQ,KAAK,EAAE,QAAQ;YACzB,MAAM,KAAK,GAAG;gBACZ,MAAM;oBACJ,QAAQ;wBAAE,IAAI,QAAQ,KAAK;oBAAC;gBAC9B;YACF;QACF;QAEA,IAAI,QAAQ,MAAM,EAAE,QAAQ;YAC1B,MAAM,OAAO,GAAG;gBAAE,IAAI,QAAQ,MAAM;YAAC;QACvC;QAEA,IAAI,QAAQ,YAAY,EAAE,QAAQ;YAChC,MAAM,WAAW,GAAG;gBAAE,IAAI,QAAQ,YAAY;YAAC;QACjD;IACF;IAEA,SAAS;IACD,aAAa,IAAS,EAA4C;QACxE,OAAQ,KAAK,KAAK;YAChB,KAAK;gBACH,OAAO;oBAAC;wBAAE,OAAO,KAAK,KAAK;oBAAC;iBAAE;YAChC,KAAK;gBACH,OAAO;oBAAC;wBAAE,WAAW,KAAK,KAAK;oBAAC;iBAAE;YACpC,KAAK;gBACH,OAAO;oBAAC;wBAAE,WAAW,KAAK,KAAK;oBAAC;iBAAE;YACpC,KAAK;gBACH,OAAO;oBAAC;wBAAE,MAAM,KAAK,KAAK;oBAAC;iBAAE;YAC/B;gBACE,OAAO;oBAAC;wBAAE,WAAW;oBAAO;iBAAE;QAClC;IACF;IAEA,SAAS;IACT,MAAc,YACZ,KAA+B,EAC/B,OAAiD,EACjD,IAAY,EACZ,IAAY,EACqB;QACjC,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7B;YACA;YACA;YACA;YACA,SAAS;gBACP,OAAO;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;gBACA,mBAAmB;oBACjB,SAAS;wBACP,kBAAkB;oBACpB;gBACF;gBACA,OAAO;oBACL,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,OAAO;oBACL,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,iBAAiB;oBACf,MAAM;oBACN,SAAS;wBAAE,WAAW;oBAAO;oBAC7B,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,MAAM;gCACN,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;QACF;IACF;IAEA,SAAS;IACT,MAAc,gBAAgB,KAA+B,EAAmB;QAC9E,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE;QAAM;IACtC;IAEA,WAAW;IACX,MAAc,oBAAoB,SAAmC,EAAE;QACrE,yBAAyB;QACzB,aAAa;QACb,OAAO;YACL,4BAA4B,EAAE;YAC9B,gBAAgB,EAAE;YAClB,gBAAgB,EAAE;YAClB,iBAAiB,EAAE;YACnB,uBAAuB,EAAE;YACzB,YAAY;gBAAE,KAAK;gBAAG,KAAK;YAAE;QAC/B;IACF;IAEA,SAAS;IACT,MAAM,eAAe,KAAa,EAAE,QAAgB,EAAE,EAA+B;QACnF,MAAM,cAAkC,EAAE;QAE1C,IAAI,MAAM,MAAM,GAAG,GAAG,OAAO;QAE7B,MAAM,kBAAkB,MAAM,IAAI,GAAG,WAAW;QAEhD,SAAS;QACT,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,MAAM;oBAAE,UAAU;oBAAiB,MAAM;gBAAc;gBACvD,QAAQ;YACV;YACA,QAAQ;gBAAE,MAAM;YAAK;YACrB,MAAM;QACR;QAEA,SAAS,OAAO,CAAC,CAAA;YACf,YAAY,IAAI,CAAC;gBACf,MAAM,wHAAA,CAAA,aAAU,CAAC,YAAY;gBAC7B,OAAO,QAAQ,IAAI;gBACnB,OAAO,QAAQ,IAAI;YACrB;QACF;QAEA,SAAS;QACT,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,IAAI;oBACF;wBAAE,MAAM;4BAAE,UAAU;4BAAiB,MAAM;wBAAc;oBAAE;oBAC3D;wBAAE,UAAU;4BAAE,MAAM;gCAAE,SAAS;oCAAE,UAAU;oCAAiB,MAAM;gCAAc;4BAAE;wBAAE;oBAAE;iBACvF;YACH;YACA,QAAQ;gBAAE,MAAM;YAAK;YACrB,MAAM;QACR;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,YAAY,IAAI,CAAC;gBACf,MAAM,wHAAA,CAAA,aAAU,CAAC,iBAAiB;gBAClC,OAAO,WAAW,IAAI;gBACtB,OAAO,WAAW,IAAI;YACxB;QACF;QAEA,OAAO,YAAY,KAAK,CAAC,GAAG;IAC9B;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/ca%20sal%20yin/pesticide-search/src/app/api/search/suggestions/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { searchService } from '@/lib/search'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const query = searchParams.get('query') || ''\n    const limit = Number(searchParams.get('limit')) || 10\n\n    if (query.length < 2) {\n      return NextResponse.json([])\n    }\n\n    const suggestions = await searchService.getSuggestions(query, limit)\n    return NextResponse.json(suggestions)\n\n  } catch (error) {\n    console.error('Suggestions error:', error)\n    return NextResponse.json(\n      { error: '获取建议失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;QAC3C,MAAM,QAAQ,OAAO,aAAa,GAAG,CAAC,aAAa;QAEnD,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QAC7B;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC,OAAO;QAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}