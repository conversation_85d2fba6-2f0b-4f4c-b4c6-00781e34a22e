module.exports = {

"[project]/.next-internal/server/app/api/search/suggestions/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/types/search.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SearchMode": (()=>SearchMode),
    "SearchType": (()=>SearchType)
});
var SearchType = /*#__PURE__*/ function(SearchType) {
    SearchType["ALL"] = "all";
    SearchType["PRODUCT_NAME"] = "product_name";
    SearchType["ACTIVE_INGREDIENT"] = "active_ingredient";
    SearchType["CROP"] = "crop";
    SearchType["PEST"] = "pest";
    SearchType["BRAND"] = "brand";
    return SearchType;
}({});
var SearchMode = /*#__PURE__*/ function(SearchMode) {
    SearchMode["FUZZY"] = "fuzzy";
    SearchMode["EXACT"] = "exact";
    SearchMode["PHRASE"] = "phrase";
    SearchMode["SYNONYM"] = "synonym"; // 同义词匹配
    return SearchMode;
}({});
}}),
"[project]/src/lib/search.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SearchService": (()=>SearchService),
    "searchService": (()=>searchService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/search.ts [app-route] (ecmascript)");
;
;
class SearchService {
    // 主搜索方法
    async search(params) {
        const { query, type, mode, filters, sort, page, limit } = params;
        // 构建基础查询条件
        const whereClause = this.buildWhereClause(query, type, mode, filters);
        // 构建排序条件
        const orderBy = this.buildOrderBy(sort);
        // 计算偏移量
        const skip = (page - 1) * limit;
        // 执行查询
        const [products, total] = await Promise.all([
            this.getProducts(whereClause, orderBy, skip, limit),
            this.getProductCount(whereClause)
        ]);
        // 获取可用筛选选项
        const availableFilters = await this.getAvailableFilters(whereClause);
        return {
            products,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            filters: availableFilters
        };
    }
    // 构建查询条件
    buildWhereClause(query, type, mode, filters) {
        const where = {
            status: 'APPROVED' // 只显示已审核的产品
        };
        // 添加搜索条件
        if (query.trim()) {
            const searchConditions = this.buildSearchConditions(query, type, mode);
            where.AND = [
                searchConditions
            ];
        }
        // 添加筛选条件
        this.addFilters(where, filters);
        return where;
    }
    // 构建搜索条件
    buildSearchConditions(query, type, mode) {
        const normalizedQuery = query.trim().toLowerCase();
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].PRODUCT_NAME:
                return this.buildProductNameSearch(normalizedQuery, mode);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].ACTIVE_INGREDIENT:
                return this.buildActiveIngredientSearch(normalizedQuery, mode);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].CROP:
                return this.buildCropSearch(normalizedQuery, mode);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].PEST:
                return this.buildPestSearch(normalizedQuery, mode);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].BRAND:
                return this.buildBrandSearch(normalizedQuery, mode);
            default:
                return this.buildGlobalSearch(normalizedQuery, mode);
        }
    }
    // 产品名称搜索
    buildProductNameSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            name: condition
        };
    }
    // 有效成分搜索
    buildActiveIngredientSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            activeIngredients: {
                some: {
                    OR: [
                        {
                            activeIngredient: {
                                name: condition
                            }
                        },
                        {
                            activeIngredient: {
                                synonyms: {
                                    some: {
                                        synonym: condition
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        };
    }
    // 作物搜索
    buildCropSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            crops: {
                some: {
                    OR: [
                        {
                            crop: {
                                name: condition
                            }
                        },
                        {
                            crop: {
                                synonyms: {
                                    some: {
                                        synonym: condition
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        };
    }
    // 病虫草害搜索
    buildPestSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            pests: {
                some: {
                    OR: [
                        {
                            pest: {
                                name: condition
                            }
                        },
                        {
                            pest: {
                                synonyms: {
                                    some: {
                                        synonym: condition
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        };
    }
    // 品牌搜索
    buildBrandSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            OR: [
                {
                    brand: {
                        name: condition
                    }
                },
                {
                    brand: {
                        company: condition
                    }
                }
            ]
        };
    }
    // 全局搜索
    buildGlobalSearch(query, mode) {
        const condition = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchMode"].EXACT ? {
            equals: query,
            mode: 'insensitive'
        } : {
            contains: query,
            mode: 'insensitive'
        };
        return {
            OR: [
                {
                    name: condition
                },
                {
                    description: condition
                },
                {
                    brand: {
                        name: condition
                    }
                },
                {
                    brand: {
                        company: condition
                    }
                },
                {
                    activeIngredients: {
                        some: {
                            activeIngredient: {
                                name: condition
                            }
                        }
                    }
                },
                {
                    crops: {
                        some: {
                            crop: {
                                name: condition
                            }
                        }
                    }
                },
                {
                    pests: {
                        some: {
                            pest: {
                                name: condition
                            }
                        }
                    }
                }
            ]
        };
    }
    // 添加筛选条件
    addFilters(where, filters) {
        if (filters.priceMin !== undefined || filters.priceMax !== undefined) {
            where.price = {};
            if (filters.priceMin !== undefined) where.price.gte = filters.priceMin;
            if (filters.priceMax !== undefined) where.price.lte = filters.priceMax;
        }
        if (filters.activeIngredients?.length) {
            where.activeIngredients = {
                some: {
                    activeIngredientId: {
                        in: filters.activeIngredients
                    }
                }
            };
        }
        if (filters.crops?.length) {
            where.crops = {
                some: {
                    cropId: {
                        in: filters.crops
                    }
                }
            };
        }
        if (filters.pests?.length) {
            where.pests = {
                some: {
                    pestId: {
                        in: filters.pests
                    }
                }
            };
        }
        if (filters.brands?.length) {
            where.brandId = {
                in: filters.brands
            };
        }
        if (filters.formulations?.length) {
            where.formulation = {
                in: filters.formulations
            };
        }
    }
    // 构建排序条件
    buildOrderBy(sort) {
        switch(sort.field){
            case 'price':
                return [
                    {
                        price: sort.order
                    }
                ];
            case 'createdAt':
                return [
                    {
                        createdAt: sort.order
                    }
                ];
            case 'updatedAt':
                return [
                    {
                        updatedAt: sort.order
                    }
                ];
            case 'name':
                return [
                    {
                        name: sort.order
                    }
                ];
            default:
                return [
                    {
                        createdAt: 'desc'
                    }
                ];
        }
    }
    // 获取产品列表
    async getProducts(where, orderBy, skip, take) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findMany({
            where,
            orderBy,
            skip,
            take,
            include: {
                brand: true,
                submitter: {
                    select: {
                        id: true,
                        name: true,
                        role: true
                    }
                },
                activeIngredients: {
                    include: {
                        activeIngredient: true
                    }
                },
                crops: {
                    include: {
                        crop: true
                    }
                },
                pests: {
                    include: {
                        pest: true
                    }
                },
                recommendations: {
                    take: 3,
                    orderBy: {
                        createdAt: 'desc'
                    },
                    include: {
                        user: {
                            select: {
                                name: true,
                                role: true
                            }
                        }
                    }
                }
            }
        });
    }
    // 获取产品总数
    async getProductCount(where) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count({
            where
        });
    }
    // 获取可用筛选选项
    async getAvailableFilters(baseWhere) {
        // 这里可以实现获取当前搜索结果中可用的筛选选项
        // 为了简化，返回空对象
        return {
            availableActiveIngredients: [],
            availableCrops: [],
            availablePests: [],
            availableBrands: [],
            availableFormulations: [],
            priceRange: {
                min: 0,
                max: 0
            }
        };
    }
    // 获取搜索建议
    async getSuggestions(query, limit = 10) {
        const suggestions = [];
        if (query.length < 2) return suggestions;
        const normalizedQuery = query.trim().toLowerCase();
        // 产品名称建议
        const products = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findMany({
            where: {
                name: {
                    contains: normalizedQuery,
                    mode: 'insensitive'
                },
                status: 'APPROVED'
            },
            select: {
                name: true
            },
            take: 3
        });
        products.forEach((product)=>{
            suggestions.push({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].PRODUCT_NAME,
                value: product.name,
                label: product.name
            });
        });
        // 有效成分建议
        const ingredients = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].activeIngredient.findMany({
            where: {
                OR: [
                    {
                        name: {
                            contains: normalizedQuery,
                            mode: 'insensitive'
                        }
                    },
                    {
                        synonyms: {
                            some: {
                                synonym: {
                                    contains: normalizedQuery,
                                    mode: 'insensitive'
                                }
                            }
                        }
                    }
                ]
            },
            select: {
                name: true
            },
            take: 3
        });
        ingredients.forEach((ingredient)=>{
            suggestions.push({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SearchType"].ACTIVE_INGREDIENT,
                value: ingredient.name,
                label: ingredient.name
            });
        });
        return suggestions.slice(0, limit);
    }
}
const searchService = new SearchService();
}}),
"[project]/src/app/api/search/suggestions/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/search.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const query = searchParams.get('query') || '';
        const limit = Number(searchParams.get('limit')) || 10;
        if (query.length < 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json([]);
        }
        const suggestions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchService"].getSuggestions(query, limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(suggestions);
    } catch (error) {
        console.error('Suggestions error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '获取建议失败'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__28df5ab5._.js.map