{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/globals.ts"], "sourcesContent": ["// @ts-ignore\nprocess.turbopack = {};\n"], "names": [], "mappings": "AAAA,aAAa;AACb,QAAQ,SAAS,GAAG,CAAC"}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/compiled/stacktrace-parser/index.js"], "sourcesContent": ["if (typeof __nccwpck_require__ !== \"undefined\")\n  __nccwpck_require__.ab = __dirname + \"/\";\n\nvar n = \"<unknown>\";\nexport function parse(e) {\n  var r = e.split(\"\\n\");\n  return r.reduce(function (e, r) {\n    var n =\n      parseChrome(r) ||\n      parseWinjs(r) ||\n      parseGecko(r) ||\n      parseNode(r) ||\n      parseJSC(r);\n    if (n) {\n      e.push(n);\n    }\n    return e;\n  }, []);\n}\nvar a =\n  /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\nvar l = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\nfunction parseChrome(e) {\n  var r = a.exec(e);\n  if (!r) {\n    return null;\n  }\n  var u = r[2] && r[2].indexOf(\"native\") === 0;\n  var t = r[2] && r[2].indexOf(\"eval\") === 0;\n  var i = l.exec(r[2]);\n  if (t && i != null) {\n    r[2] = i[1];\n    r[3] = i[2];\n    r[4] = i[3];\n  }\n  return {\n    file: !u ? r[2] : null,\n    methodName: r[1] || n,\n    arguments: u ? [r[2]] : [],\n    lineNumber: r[3] ? +r[3] : null,\n    column: r[4] ? +r[4] : null,\n  };\n}\nvar u =\n  /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\nfunction parseWinjs(e) {\n  var r = u.exec(e);\n  if (!r) {\n    return null;\n  }\n  return {\n    file: r[2],\n    methodName: r[1] || n,\n    arguments: [],\n    lineNumber: +r[3],\n    column: r[4] ? +r[4] : null,\n  };\n}\nvar t =\n  /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nvar i = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\nfunction parseGecko(e) {\n  var r = t.exec(e);\n  if (!r) {\n    return null;\n  }\n  var a = r[3] && r[3].indexOf(\" > eval\") > -1;\n  var l = i.exec(r[3]);\n  if (a && l != null) {\n    r[3] = l[1];\n    r[4] = l[2];\n    r[5] = null;\n  }\n  return {\n    file: r[3],\n    methodName: r[1] || n,\n    arguments: r[2] ? r[2].split(\",\") : [],\n    lineNumber: r[4] ? +r[4] : null,\n    column: r[5] ? +r[5] : null,\n  };\n}\nvar s = /^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;\nfunction parseJSC(e) {\n  var r = s.exec(e);\n  if (!r) {\n    return null;\n  }\n  return {\n    file: r[3],\n    methodName: r[1] || n,\n    arguments: [],\n    lineNumber: +r[4],\n    column: r[5] ? +r[5] : null,\n  };\n}\nvar o =\n  /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\nfunction parseNode(e) {\n  var r = o.exec(e);\n  if (!r) {\n    return null;\n  }\n  return {\n    file: r[2],\n    methodName: r[1] || n,\n    arguments: [],\n    lineNumber: +r[3],\n    column: r[4] ? +r[4] : null,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,OAAO,wBAAwB,aACjC,oBAAoB,EAAE,GAAG,YAAY;AAEvC,IAAI,IAAI;AACD,SAAS,MAAM,CAAC;IACrB,IAAI,IAAI,EAAE,KAAK,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;QAC5B,IAAI,IACF,YAAY,MACZ,WAAW,MACX,WAAW,MACX,UAAU,MACV,SAAS;QACX,IAAI,GAAG;YACL,EAAE,IAAI,CAAC;QACT;QACA,OAAO;IACT,GAAG,EAAE;AACP;AACA,IAAI,IACF;AACF,IAAI,IAAI;AACR,SAAS,YAAY,CAAC;IACpB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc;IAC3C,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY;IACzC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IACnB,IAAI,KAAK,KAAK,MAAM;QAClB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;QACL,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG;QAClB,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,IAAI;YAAC,CAAC,CAAC,EAAE;SAAC,GAAG,EAAE;QAC1B,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;QAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,SAAS,WAAW,CAAC;IACnB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,IAAI,IAAI;AACR,SAAS,WAAW,CAAC;IACnB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC;IAC3C,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IACnB,IAAI,KAAK,KAAK,MAAM;QAClB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QACtC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;QAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IAAI;AACR,SAAS,SAAS,CAAC;IACjB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,SAAS,UAAU,CAAC;IAClB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF"}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/error.ts"], "sourcesContent": ["// merged from next.js\n// https://github.com/vercel/next.js/blob/e657741b9908cf0044aaef959c0c4defb19ed6d8/packages/next/src/lib/is-error.ts\n// https://github.com/vercel/next.js/blob/e657741b9908cf0044aaef959c0c4defb19ed6d8/packages/next/src/shared/lib/is-plain-object.ts\n\nexport default function isError(err: unknown): err is Error {\n  return (\n    typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err\n  );\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err;\n  }\n\n  if (process.env.NODE_ENV === \"development\") {\n    // Provide a better error message for cases where `throw undefined`\n    // is called in development\n    if (typeof err === \"undefined\") {\n      return new Error(\"`undefined` was thrown instead of a real error\");\n    }\n\n    if (err === null) {\n      return new Error(\"`null` was thrown instead of a real error\");\n    }\n  }\n\n  return new Error(isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\nfunction getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value);\n}\n\nfunction isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== \"[object Object]\") {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(value);\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty(\"isPrototypeOf\");\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,oHAAoH;AACpH,kIAAkI;;;;;AAEnH,SAAS,QAAQ,GAAY;IAC1C,OACE,OAAO,QAAQ,YAAY,QAAQ,QAAQ,UAAU,OAAO,aAAa;AAE7E;AAEO,SAAS,eAAe,GAAY;IACzC,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IAEA,wCAA4C;QAC1C,mEAAmE;QACnE,2BAA2B;QAC3B,IAAI,OAAO,QAAQ,aAAa;YAC9B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,QAAQ,MAAM;YAChB,OAAO,IAAI,MAAM;QACnB;IACF;IAEA,OAAO,IAAI,MAAM,cAAc,OAAO,KAAK,SAAS,CAAC,OAAO,MAAM;AACpE;AAEA,SAAS,oBAAoB,KAAU;IACrC,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AACxC;AAEA,SAAS,cAAc,KAAU;IAC/B,IAAI,oBAAoB,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,cAAc,CAAC;IAExC;;;;;;;;GAQC,GACD,OAAO,cAAc,QAAQ,UAAU,cAAc,CAAC;AACxD"}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/index.ts"], "sourcesContent": ["import { createConnection } from \"node:net\";\nimport type { StackFrame } from \"../compiled/stacktrace-parser\";\nimport { parse as parseStackTrace } from \"../compiled/stacktrace-parser\";\nimport { getProperError } from \"./error\";\n\nexport type StructuredError = {\n  name: string;\n  message: string;\n  stack: StackFrame[];\n  cause: StructuredError | undefined\n};\n\nexport function structuredError(e: Error): StructuredError {\n  e = getProperError(e);\n\n  return {\n    name: e.name,\n    message: e.message,\n    stack: typeof e.stack === \"string\" ? parseStackTrace(e.stack!) : [],\n    cause: e.cause ? structuredError(getProperError(e.cause)) : undefined,\n  };\n}\n\ntype State =\n  | {\n      type: \"waiting\";\n    }\n  | {\n      type: \"packet\";\n      length: number;\n    };\n\nexport type Ipc<TIncoming, TOutgoing> = {\n  recv(): Promise<TIncoming>;\n  send(message: TOutgoing): Promise<void>;\n  sendError(error: Error): Promise<never>;\n  sendReady(): Promise<void>;\n};\n\nfunction createIpc<TIncoming, TOutgoing>(\n  port: number\n): Ipc<TIncoming, TOutgoing> {\n  const socket = createConnection(port, \"127.0.0.1\");\n  const packetQueue: Buffer[] = [];\n  const recvPromiseResolveQueue: Array<(message: TIncoming) => void> = [];\n\n  function pushPacket(packet: Buffer) {\n    const recvPromiseResolve = recvPromiseResolveQueue.shift();\n    if (recvPromiseResolve != null) {\n      recvPromiseResolve(JSON.parse(packet.toString(\"utf8\")) as TIncoming);\n    } else {\n      packetQueue.push(packet);\n    }\n  }\n\n  let state: State = { type: \"waiting\" };\n  let buffer: Buffer = Buffer.alloc(0);\n  socket.once(\"connect\", () => {\n    socket.on(\"data\", (chunk) => {\n      buffer = Buffer.concat([buffer, chunk]);\n\n      loop: while (true) {\n        switch (state.type) {\n          case \"waiting\": {\n            if (buffer.length >= 4) {\n              const length = buffer.readUInt32BE(0);\n              buffer = buffer.subarray(4);\n              state = { type: \"packet\", length };\n            } else {\n              break loop;\n            }\n            break;\n          }\n          case \"packet\": {\n            if (buffer.length >= state.length) {\n              const packet = buffer.subarray(0, state.length);\n              buffer = buffer.subarray(state.length);\n              state = { type: \"waiting\" };\n              pushPacket(packet);\n            } else {\n              break loop;\n            }\n            break;\n          }\n          default:\n            invariant(state, (state) => `Unknown state type: ${state?.type}`);\n        }\n      }\n    });\n  });\n  // When the socket is closed, this process is no longer needed.\n  // This might happen e. g. when parent process is killed or\n  // node.js pool is garbage collected.\n  socket.once(\"close\", () => {\n    process.exit(0);\n  });\n\n  function send(message: any): Promise<void> {\n    const packet = Buffer.from(JSON.stringify(message), \"utf8\");\n    const length = Buffer.alloc(4);\n    length.writeUInt32BE(packet.length);\n    socket.write(length);\n\n    return new Promise((resolve, reject) => {\n      socket.write(packet, (err) => {\n        process.stderr.write(`TURBOPACK_OUTPUT_D\\n`);\n        process.stdout.write(`TURBOPACK_OUTPUT_D\\n`);\n        if (err != null) {\n          reject(err);\n        } else {\n          resolve();\n        }\n      });\n    });\n  }\n\n  function sendReady(): Promise<void> {\n    const length = Buffer.from([0, 0, 0, 0]);\n    return new Promise((resolve, reject) => {\n      socket.write(length, (err) => {\n        process.stderr.write(`TURBOPACK_OUTPUT_D\\n`);\n        process.stdout.write(`TURBOPACK_OUTPUT_D\\n`);\n\n        if (err != null) {\n          reject(err);\n        } else {\n          resolve();\n        }\n      });\n    });\n  }\n\n  return {\n    async recv() {\n      const packet = packetQueue.shift();\n      if (packet != null) {\n        return JSON.parse(packet.toString(\"utf8\")) as TIncoming;\n      }\n\n      const result = await new Promise<TIncoming>((resolve) => {\n        recvPromiseResolveQueue.push((result) => {\n          resolve(result);\n        });\n      });\n\n      return result;\n    },\n\n    send(message: TOutgoing) {\n      return send(message);\n    },\n\n    sendReady,\n\n    async sendError(error: Error): Promise<never> {\n      try {\n        await send({\n          type: \"error\",\n          ...structuredError(error),\n        });\n      } catch (err) {\n        console.error(\"failed to send error back to rust:\", err);\n        // ignore and exit anyway\n        process.exit(1);\n      }\n\n      process.exit(0);\n    },\n  };\n}\n\nconst PORT = process.argv[2];\n\nexport const IPC = createIpc<unknown, unknown>(parseInt(PORT, 10));\n\nprocess.on(\"uncaughtException\", (err) => {\n  IPC.sendError(err);\n});\n\nconst improveConsole = (name: string, stream: string, addStack: boolean) => {\n  // @ts-ignore\n  const original = console[name];\n  // @ts-ignore\n  const stdio = process[stream];\n  // @ts-ignore\n  console[name] = (...args: any[]) => {\n    stdio.write(`TURBOPACK_OUTPUT_B\\n`);\n    original(...args);\n    if (addStack) {\n      const stack = new Error().stack?.replace(/^.+\\n.+\\n/, \"\") + \"\\n\";\n      stdio.write(\"TURBOPACK_OUTPUT_S\\n\");\n      stdio.write(stack);\n    }\n    stdio.write(\"TURBOPACK_OUTPUT_E\\n\");\n  };\n};\n\nimproveConsole(\"error\", \"stderr\", true);\nimproveConsole(\"warn\", \"stderr\", true);\nimproveConsole(\"count\", \"stdout\", true);\nimproveConsole(\"trace\", \"stderr\", false);\nimproveConsole(\"log\", \"stdout\", true);\nimproveConsole(\"group\", \"stdout\", true);\nimproveConsole(\"groupCollapsed\", \"stdout\", true);\nimproveConsole(\"table\", \"stdout\", true);\nimproveConsole(\"debug\", \"stdout\", true);\nimproveConsole(\"info\", \"stdout\", true);\nimproveConsole(\"dir\", \"stdout\", true);\nimproveConsole(\"dirxml\", \"stdout\", true);\nimproveConsole(\"timeEnd\", \"stdout\", true);\nimproveConsole(\"timeLog\", \"stdout\", true);\nimproveConsole(\"timeStamp\", \"stdout\", true);\nimproveConsole(\"assert\", \"stderr\", true);\n\n/**\n * Utility function to ensure all variants of an enum are handled.\n */\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\n  throw new Error(`Invariant: ${computeMessage(never)}`);\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AASO,SAAS,gBAAgB,CAAQ;IACtC,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;IAEnB,OAAO;QACL,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,OAAO;QAClB,OAAO,OAAO,EAAE,KAAK,KAAK,WAAW,CAAA,GAAA,gJAAA,CAAA,QAAe,AAAD,EAAE,EAAE,KAAK,IAAK,EAAE;QACnE,OAAO,EAAE,KAAK,GAAG,gBAAgB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,EAAE,KAAK,KAAK;IAC9D;AACF;AAkBA,SAAS,UACP,IAAY;IAEZ,MAAM,SAAS,CAAA,GAAA,+GAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IACtC,MAAM,cAAwB,EAAE;IAChC,MAAM,0BAA+D,EAAE;IAEvE,SAAS,WAAW,MAAc;QAChC,MAAM,qBAAqB,wBAAwB,KAAK;QACxD,IAAI,sBAAsB,MAAM;YAC9B,mBAAmB,KAAK,KAAK,CAAC,OAAO,QAAQ,CAAC;QAChD,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,IAAI,QAAe;QAAE,MAAM;IAAU;IACrC,IAAI,SAAiB,OAAO,KAAK,CAAC;IAClC,OAAO,IAAI,CAAC,WAAW;QACrB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACjB,SAAS,OAAO,MAAM,CAAC;gBAAC;gBAAQ;aAAM;YAEtC,MAAM,MAAO,KAAM;gBACjB,OAAQ,MAAM,IAAI;oBAChB,KAAK;wBAAW;4BACd,IAAI,OAAO,MAAM,IAAI,GAAG;gCACtB,MAAM,SAAS,OAAO,YAAY,CAAC;gCACnC,SAAS,OAAO,QAAQ,CAAC;gCACzB,QAAQ;oCAAE,MAAM;oCAAU;gCAAO;4BACnC,OAAO;gCACL,MAAM;4BACR;4BACA;wBACF;oBACA,KAAK;wBAAU;4BACb,IAAI,OAAO,MAAM,IAAI,MAAM,MAAM,EAAE;gCACjC,MAAM,SAAS,OAAO,QAAQ,CAAC,GAAG,MAAM,MAAM;gCAC9C,SAAS,OAAO,QAAQ,CAAC,MAAM,MAAM;gCACrC,QAAQ;oCAAE,MAAM;gCAAU;gCAC1B,WAAW;4BACb,OAAO;gCACL,MAAM;4BACR;4BACA;wBACF;oBACA;wBACE,UAAU,OAAO,CAAC,QAAU,CAAC,oBAAoB,EAAE,OAAO,MAAM;gBACpE;YACF;QACF;IACF;IACA,+DAA+D;IAC/D,2DAA2D;IAC3D,qCAAqC;IACrC,OAAO,IAAI,CAAC,SAAS;QACnB,QAAQ,IAAI,CAAC;IACf;IAEA,SAAS,KAAK,OAAY;QACxB,MAAM,SAAS,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,UAAU;QACpD,MAAM,SAAS,OAAO,KAAK,CAAC;QAC5B,OAAO,aAAa,CAAC,OAAO,MAAM;QAClC,OAAO,KAAK,CAAC;QAEb,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,OAAO,KAAK,CAAC,QAAQ,CAAC;gBACpB,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,IAAI,OAAO,MAAM;oBACf,OAAO;gBACT,OAAO;oBACL;gBACF;YACF;QACF;IACF;IAEA,SAAS;QACP,MAAM,SAAS,OAAO,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;SAAE;QACvC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,OAAO,KAAK,CAAC,QAAQ,CAAC;gBACpB,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAE3C,IAAI,OAAO,MAAM;oBACf,OAAO;gBACT,OAAO;oBACL;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACL,MAAM;YACJ,MAAM,SAAS,YAAY,KAAK;YAChC,IAAI,UAAU,MAAM;gBAClB,OAAO,KAAK,KAAK,CAAC,OAAO,QAAQ,CAAC;YACpC;YAEA,MAAM,SAAS,MAAM,IAAI,QAAmB,CAAC;gBAC3C,wBAAwB,IAAI,CAAC,CAAC;oBAC5B,QAAQ;gBACV;YACF;YAEA,OAAO;QACT;QAEA,MAAK,OAAkB;YACrB,OAAO,KAAK;QACd;QAEA;QAEA,MAAM,WAAU,KAAY;YAC1B,IAAI;gBACF,MAAM,KAAK;oBACT,MAAM;oBACN,GAAG,gBAAgB,MAAM;gBAC3B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,yBAAyB;gBACzB,QAAQ,IAAI,CAAC;YACf;YAEA,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAEA,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE;AAErB,MAAM,MAAM,UAA4B,SAAS,MAAM;AAE9D,QAAQ,EAAE,CAAC,qBAAqB,CAAC;IAC/B,IAAI,SAAS,CAAC;AAChB;AAEA,MAAM,iBAAiB,CAAC,MAAc,QAAgB;IACpD,aAAa;IACb,MAAM,WAAW,OAAO,CAAC,KAAK;IAC9B,aAAa;IACb,MAAM,QAAQ,OAAO,CAAC,OAAO;IAC7B,aAAa;IACb,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG;QAClB,MAAM,KAAK,CAAC,CAAC,oBAAoB,CAAC;QAClC,YAAY;QACZ,IAAI,UAAU;YACZ,MAAM,QAAQ,IAAI,QAAQ,KAAK,EAAE,QAAQ,aAAa,MAAM;YAC5D,MAAM,KAAK,CAAC;YACZ,MAAM,KAAK,CAAC;QACd;QACA,MAAM,KAAK,CAAC;IACd;AACF;AAEA,eAAe,SAAS,UAAU;AAClC,eAAe,QAAQ,UAAU;AACjC,eAAe,SAAS,UAAU;AAClC,eAAe,SAAS,UAAU;AAClC,eAAe,OAAO,UAAU;AAChC,eAAe,SAAS,UAAU;AAClC,eAAe,kBAAkB,UAAU;AAC3C,eAAe,SAAS,UAAU;AAClC,eAAe,SAAS,UAAU;AAClC,eAAe,QAAQ,UAAU;AACjC,eAAe,OAAO,UAAU;AAChC,eAAe,UAAU,UAAU;AACnC,eAAe,WAAW,UAAU;AACpC,eAAe,WAAW,UAAU;AACpC,eAAe,aAAa,UAAU;AACtC,eAAe,UAAU,UAAU;AAEnC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD"}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/evaluate.ts"], "sourcesContent": ["import { IPC } from \"./index\";\nimport type { Ipc as GenericIpc } from \"./index\";\n\ntype IpcIncomingMessage =\n  | {\n      type: \"evaluate\";\n      args: string[];\n    }\n  | {\n      type: \"result\";\n      id: number;\n      error: string | null;\n      data: any | null;\n    };\n\ntype IpcOutgoingMessage =\n  | {\n      type: \"end\";\n      data: string | undefined;\n      duration: number;\n    }\n  | {\n      type: \"info\";\n      data: any;\n    }\n  | {\n      type: \"request\";\n      id: number;\n      data: any;\n    };\n\nexport type Ipc<IM, RM> = {\n  sendInfo(message: IM): Promise<void>;\n  sendRequest(message: RM): Promise<unknown>;\n  sendError(error: Error): Promise<never>;\n};\nconst ipc = IPC as GenericIpc<IpcIncomingMessage, IpcOutgoingMessage>;\n\nconst queue: string[][] = [];\n\nexport const run = async (\n  moduleFactory: () => Promise<{\n    init?: () => Promise<void>;\n    default: (ipc: Ipc<any, any>, ...deserializedArgs: any[]) => any;\n  }>\n) => {\n  let nextId = 1;\n  const requests = new Map();\n  const internalIpc = {\n    sendInfo: (message: any) =>\n      ipc.send({\n        type: \"info\",\n        data: message,\n      }),\n    sendRequest: (message: any) => {\n      const id = nextId++;\n      let resolve, reject;\n      const promise = new Promise((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n      requests.set(id, { resolve, reject });\n      return ipc\n        .send({ type: \"request\", id, data: message })\n        .then(() => promise);\n    },\n    sendError: (error: Error) => {\n      return ipc.sendError(error);\n    },\n  };\n\n  // Initialize module and send ready message\n  let getValue: (ipc: Ipc<any, any>, ...deserializedArgs: any[]) => any;\n  try {\n    const module = await moduleFactory();\n    if (typeof module.init === \"function\") {\n      await module.init();\n    }\n    getValue = module.default;\n    await ipc.sendReady();\n  } catch (err) {\n    await ipc.sendReady();\n    await ipc.sendError(err as Error);\n  }\n\n  // Queue handling\n  let isRunning = false;\n  const run = async () => {\n    while (queue.length > 0) {\n      const args = queue.shift()!;\n      try {\n        const value = await getValue(internalIpc, ...args);\n        await ipc.send({\n          type: \"end\",\n          data:\n            value === undefined ? undefined : JSON.stringify(value, null, 2),\n          duration: 0,\n        });\n      } catch (e) {\n        await ipc.sendError(e as Error);\n      }\n    }\n    isRunning = false;\n  };\n\n  // Communication handling\n  while (true) {\n    const msg = await ipc.recv();\n\n    switch (msg.type) {\n      case \"evaluate\": {\n        queue.push(msg.args);\n        if (!isRunning) {\n          isRunning = true;\n          run();\n        }\n        break;\n      }\n      case \"result\": {\n        const request = requests.get(msg.id);\n        if (request) {\n          requests.delete(msg.id);\n          if (msg.error) {\n            request.reject(new Error(msg.error));\n          } else {\n            request.resolve(msg.data);\n          }\n        }\n        break;\n      }\n      default: {\n        console.error(\"unexpected message type\", (msg as any).type);\n        process.exit(1);\n      }\n    }\n  }\n};\n\nexport type { IpcIncomingMessage, IpcOutgoingMessage };\n"], "names": [], "mappings": ";;;AAAA;;AAoCA,MAAM,MAAM,mHAAA,CAAA,MAAG;AAEf,MAAM,QAAoB,EAAE;AAErB,MAAM,MAAM,OACjB;IAKA,IAAI,SAAS;IACb,MAAM,WAAW,IAAI;IACrB,MAAM,cAAc;QAClB,UAAU,CAAC,UACT,IAAI,IAAI,CAAC;gBACP,MAAM;gBACN,MAAM;YACR;QACF,aAAa,CAAC;YACZ,MAAM,KAAK;YACX,IAAI,SAAS;YACb,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK;gBAChC,UAAU;gBACV,SAAS;YACX;YACA,SAAS,GAAG,CAAC,IAAI;gBAAE;gBAAS;YAAO;YACnC,OAAO,IACJ,IAAI,CAAC;gBAAE,MAAM;gBAAW;gBAAI,MAAM;YAAQ,GAC1C,IAAI,CAAC,IAAM;QAChB;QACA,WAAW,CAAC;YACV,OAAO,IAAI,SAAS,CAAC;QACvB;IACF;IAEA,2CAA2C;IAC3C,IAAI;IACJ,IAAI;QACF,MAAM,SAAS,MAAM;QACrB,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;YACrC,MAAM,OAAO,IAAI;QACnB;QACA,WAAW,OAAO,OAAO;QACzB,MAAM,IAAI,SAAS;IACrB,EAAE,OAAO,KAAK;QACZ,MAAM,IAAI,SAAS;QACnB,MAAM,IAAI,SAAS,CAAC;IACtB;IAEA,iBAAiB;IACjB,IAAI,YAAY;IAChB,MAAM,MAAM;QACV,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,OAAO,MAAM,KAAK;YACxB,IAAI;gBACF,MAAM,QAAQ,MAAM,SAAS,gBAAgB;gBAC7C,MAAM,IAAI,IAAI,CAAC;oBACb,MAAM;oBACN,MACE,UAAU,YAAY,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;oBAChE,UAAU;gBACZ;YACF,EAAE,OAAO,GAAG;gBACV,MAAM,IAAI,SAAS,CAAC;YACtB;QACF;QACA,YAAY;IACd;IAEA,yBAAyB;IACzB,MAAO,KAAM;QACX,MAAM,MAAM,MAAM,IAAI,IAAI;QAE1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBAAY;oBACf,MAAM,IAAI,CAAC,IAAI,IAAI;oBACnB,IAAI,CAAC,WAAW;wBACd,YAAY;wBACZ;oBACF;oBACA;gBACF;YACA,KAAK;gBAAU;oBACb,MAAM,UAAU,SAAS,GAAG,CAAC,IAAI,EAAE;oBACnC,IAAI,SAAS;wBACX,SAAS,MAAM,CAAC,IAAI,EAAE;wBACtB,IAAI,IAAI,KAAK,EAAE;4BACb,QAAQ,MAAM,CAAC,IAAI,MAAM,IAAI,KAAK;wBACpC,OAAO;4BACL,QAAQ,OAAO,CAAC,IAAI,IAAI;wBAC1B;oBACF;oBACA;gBACF;YACA;gBAAS;oBACP,QAAQ,KAAK,CAAC,2BAA2B,AAAC,IAAY,IAAI;oBAC1D,QAAQ,IAAI,CAAC;gBACf;QACF;IACF;AACF"}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/evaluate.ts/evaluate.js"], "sourcesContent": ["import { run } from 'RUNTIME'; run(() => import('INNER'))"], "names": [], "mappings": ";AAAA;;AAA+B,CAAA,GAAA,sHAAA,CAAA,MAAG,AAAD,EAAE"}}]}