// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户角色枚举
enum UserRole {
  ADMIN // 管理员
  AGRONOMIST // 农艺师
  PESTICIDE_STORE // 农药店
  BRAND // 品牌方
}

// 用户状态枚举
enum UserStatus {
  PENDING // 待审核
  APPROVED // 已审核
  REJECTED // 已拒绝
  SUSPENDED // 已暂停
}

// 产品状态枚举
enum ProductStatus {
  PENDING // 待审核
  APPROVED // 已审核
  REJECTED // 已拒绝
  INACTIVE // 已下架
}

// 用户表
model User {
  id          String     @id @default(cuid())
  email       String     @unique
  name        String
  phone       String?
  role        UserRole
  status      UserStatus @default(PENDING)
  companyName String? // 公司名称
  license     String? // 营业执照或资质证书
  description String? // 描述
  avatar      String? // 头像
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // 关联关系
  products        Product[]
  recommendations Recommendation[]
  auditLogs       AuditLog[]

  @@map("users")
}

// 有效成分表
model ActiveIngredient {
  id          String   @id @default(cuid())
  name        String   @unique // 有效成分名称
  casNumber   String?  @unique // CAS号
  description String? // 描述
  toxicity    String? // 毒性等级
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  products ProductActiveIngredient[]
  synonyms ActiveIngredientSynonym[]

  @@map("active_ingredients")
}

// 有效成分同义词表
model ActiveIngredientSynonym {
  id                 String           @id @default(cuid())
  synonym            String // 同义词
  activeIngredientId String
  activeIngredient   ActiveIngredient @relation(fields: [activeIngredientId], references: [id], onDelete: Cascade)

  @@unique([synonym, activeIngredientId])
  @@map("active_ingredient_synonyms")
}

// 作物表
model Crop {
  id        String   @id @default(cuid())
  name      String   @unique // 作物名称
  category  String? // 作物分类
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  products ProductCrop[]
  pests    CropPest[]
  synonyms CropSynonym[]

  @@map("crops")
}

// 作物同义词表
model CropSynonym {
  id      String @id @default(cuid())
  synonym String // 同义词
  cropId  String
  crop    Crop   @relation(fields: [cropId], references: [id], onDelete: Cascade)

  @@unique([synonym, cropId])
  @@map("crop_synonyms")
}

// 病虫草害表
model Pest {
  id          String   @id @default(cuid())
  name        String   @unique // 病虫草害名称
  type        String // 类型：病害、虫害、草害
  description String? // 描述
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  products ProductPest[]
  crops    CropPest[]
  synonyms PestSynonym[]

  @@map("pests")
}

// 病虫草害同义词表
model PestSynonym {
  id      String @id @default(cuid())
  synonym String // 同义词
  pestId  String
  pest    Pest   @relation(fields: [pestId], references: [id], onDelete: Cascade)

  @@unique([synonym, pestId])
  @@map("pest_synonyms")
}

// 作物-病虫草害关联表
model CropPest {
  id     String @id @default(cuid())
  cropId String
  pestId String
  crop   Crop   @relation(fields: [cropId], references: [id], onDelete: Cascade)
  pest   Pest   @relation(fields: [pestId], references: [id], onDelete: Cascade)

  @@unique([cropId, pestId])
  @@map("crop_pests")
}

// 品牌表
model Brand {
  id          String   @id @default(cuid())
  name        String   @unique // 品牌名称
  company     String // 公司名称
  description String? // 品牌描述
  logo        String? // 品牌logo
  website     String? // 官网
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  products Product[]

  @@map("brands")
}

// 产品表
model Product {
  id             String        @id @default(cuid())
  name           String // 商品名称
  brandId        String // 品牌ID
  registrationNo String? // 登记证号
  formulation    String? // 剂型
  concentration  String? // 含量
  packageSize    String? // 包装规格
  price          Decimal? // 价格
  priceUnit      String? // 价格单位
  description    String? // 产品描述
  usage          String? // 使用方法
  precautions    String? // 注意事项
  images         String[] // 产品图片
  status         ProductStatus @default(PENDING)
  submittedBy    String // 提交者ID
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // 关联关系
  brand             Brand                     @relation(fields: [brandId], references: [id])
  submitter         User                      @relation(fields: [submittedBy], references: [id])
  activeIngredients ProductActiveIngredient[]
  crops             ProductCrop[]
  pests             ProductPest[]
  recommendations   Recommendation[]
  auditLogs         AuditLog[]

  @@map("products")
}

// 产品-有效成分关联表
model ProductActiveIngredient {
  id                 String           @id @default(cuid())
  productId          String
  activeIngredientId String
  concentration      String? // 该成分在产品中的含量
  product            Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  activeIngredient   ActiveIngredient @relation(fields: [activeIngredientId], references: [id], onDelete: Cascade)

  @@unique([productId, activeIngredientId])
  @@map("product_active_ingredients")
}

// 产品-作物关联表
model ProductCrop {
  id        String  @id @default(cuid())
  productId String
  cropId    String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  crop      Crop    @relation(fields: [cropId], references: [id], onDelete: Cascade)

  @@unique([productId, cropId])
  @@map("product_crops")
}

// 产品-病虫草害关联表
model ProductPest {
  id        String  @id @default(cuid())
  productId String
  pestId    String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  pest      Pest    @relation(fields: [pestId], references: [id], onDelete: Cascade)

  @@unique([productId, pestId])
  @@map("product_pests")
}

// 推荐表（农艺师推荐产品）
model Recommendation {
  id        String   @id @default(cuid())
  productId String
  userId    String // 推荐者ID（农艺师）
  title     String // 推荐标题
  content   String // 推荐内容
  rating    Int? // 评分 1-5
  isPublic  Boolean  @default(true) // 是否公开
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@map("recommendations")
}

// 审核日志表
model AuditLog {
  id         String   @id @default(cuid())
  entityType String // 实体类型：User, Product
  entityId   String // 实体ID
  action     String // 操作：APPROVE, REJECT, SUSPEND
  reason     String? // 审核原因
  auditorId  String // 审核员ID
  createdAt  DateTime @default(now())

  // 关联关系
  auditor User     @relation(fields: [auditorId], references: [id])
  product Product? @relation(fields: [entityId], references: [id])

  @@map("audit_logs")
}
